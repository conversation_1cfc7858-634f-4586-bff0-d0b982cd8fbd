-- 简化的测试存储过程
-- 用于验证OUTPUT参数转换是否正确

CREATE OR REPLACE FUNCTION test_simple_procedure(
    input_xml TEXT                  -- 输入XML
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    result_value VARCHAR(50);       -- 返回值变量
    test_flag CHAR(1);             -- 测试标志
BEGIN
    -- 初始化返回值
    result_value := '0';
    
    -- 简单的业务逻辑
    test_flag := '1';
    
    IF test_flag = '1' THEN
        result_value := '1成功';
    ELSE
        result_value := '0失败';
    END IF;
    
    -- 返回结果
    RETURN result_value;
END;
$BODY$ LANGUAGE plpgsql;

-- 测试调用
-- SELECT test_simple_procedure('<test>data</test>');
