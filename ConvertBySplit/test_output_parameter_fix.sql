-- 测试OUTPUT参数修复的简单示例

-- 原始SQL Server存储过程（示例）
/*
ALTER PROCEDURE dbo.TestOutputParam
    @inputValue NTEXT,
    @outputResult VARCHAR(50) OUTPUT
AS
BEGIN
    SET @outputResult = '1Success'
END
*/

-- 转换后的KingBase函数
CREATE OR REPLACE FUNCTION TestOutputParam(
    inputValue TEXT
) RETURNS VARCHAR(50) AS $BODY$
BEGIN
    -- 业务逻辑，直接返回结果
    IF inputValue IS NOT NULL AND LENGTH(inputValue) > 0 THEN
        RETURN '1Success';
    ELSE
        RETURN '0Failed';
    END IF;
END;
$BODY$ LANGUAGE plpgsql;

-- 测试调用
-- SELECT TestOutputParam('test data');
-- 预期结果: '1Success'

-- SELECT TestOutputParam('');
-- 预期结果: '0Failed'
