-- 测试OPENXML转换修复的示例

-- 原始错误的转换结果（会产生语法错误）
/*
        -- 原始: exec sp_xml_preparedocument intDoc, chrXML SELECT
        -- KingBase不支持sp_xml_preparedocument，需要使用XML函数替代
        (xpath('/root/clue/LID/text()', intDoc::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/clue/SalesOrder/text()', intDoc::xml))[1]::text::VARCHAR AS SalesOrder
        FROM (SELECT 1) AS dummy_table;

        -- 注意：以下字段定义应该在CREATE TABLE语句中定义
        -- CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))

        select intLID=LID FROM temp_DataM  ;
*/

-- 修复后的正确转换结果
-- 原始: exec sp_xml_preparedocument intDoc, chrXML
-- KingBase不支持sp_xml_preparedocument，需要使用XML函数替代
CREATE TEMP TABLE temp_DataM AS
SELECT
    (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
    (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,
    (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,
    (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,
    (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;

-- 获取变量值
SELECT LID INTO intLID FROM temp_DataM LIMIT 1;

-- 完整的函数示例
CREATE OR REPLACE FUNCTION test_openxml_fix(chrXML TEXT) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    intLID INTEGER;
BEGIN
    -- 创建临时表
    CREATE TEMP TABLE temp_DataM AS
    SELECT
        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,
        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,
        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,
        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;

    -- 获取LID值
    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;

    -- 更新操作
    UPDATE crmClueM 
    SET 
        SalesOrder = b.SalesOrder,
        CustomerInfo = b.CustomerInfo,
        LostReason = b.LostReason,
        Suggestion = b.Suggestion
    FROM temp_DataM b 
    WHERE crmClueM.LID = b.LID AND crmClueM.LID = intLID;

    -- 清理
    DROP TABLE IF EXISTS temp_DataM;

    RETURN '1';
EXCEPTION
    WHEN OTHERS THEN
        DROP TABLE IF EXISTS temp_DataM;
        RETURN '0';
END;
$BODY$ LANGUAGE plpgsql;

-- 测试调用
-- SELECT test_openxml_fix('<root><clue><LID>123</LID><SalesOrder>SO001</SalesOrder></clue></root>');
