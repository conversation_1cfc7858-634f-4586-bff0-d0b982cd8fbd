-- 测试表提示和数据类型修复的示例

-- 原始错误的SQL Server代码
/*
DECLARE @chrDocCode VARCHAR(10);
DECLARE @chrSql VARCHAR(max);
DECLARE @chrSysPara1 VARCHAR(300);
DECLARE @chrSysPara2 VARCHAR(300);
DECLARE @chrNo2 VARCHAR(30);

-- SELECT TOP 1 chrDoccode=DocCode FROM wahInvoice
SET @chrDoccode='08';

IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WITH(NOLOCK) WHERE DocNo=@chrNo AND AuCode='014')
BEGIN
    SET @chrIfAuCode='1'
END
*/

-- 修复后的KingBase兼容代码
DECLARE
    chrDocCode VARCHAR(10);
    chrSql TEXT;                    -- VARCHAR(max) -> TEXT
    chrSysPara1 VARCHAR(300);
    chrSysPara2 VARCHAR(300);
    chrNo2 VARCHAR(30);
    chrIfAuCode CHAR(1);
    chrNo VARCHAR(30);
BEGIN
    -- SELECT TOP 1 chrDoccode=DocCode FROM wahInvoice
    chrDoccode := '08';

    -- 移除了WITH(NOLOCK)表提示
    IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
        chrIfAuCode := '1';
    END IF;
END;

-- 完整的函数示例
CREATE OR REPLACE FUNCTION test_table_hints_fix(
    chrNo VARCHAR(30)
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    chrDocCode VARCHAR(10);
    chrSql TEXT;                    -- 修复：VARCHAR(max) -> TEXT
    chrSysPara1 VARCHAR(300);
    chrSysPara2 VARCHAR(300);
    chrNo2 VARCHAR(30);
    chrIfAuCode CHAR(1);
BEGIN
    -- 初始化变量
    chrDocCode := '08';
    chrIfAuCode := '0';

    -- 修复：移除了WITH(NOLOCK)表提示
    IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
        chrIfAuCode := '1';
    END IF;

    -- 其他业务逻辑...
    
    RETURN chrIfAuCode;
EXCEPTION
    WHEN OTHERS THEN
        RETURN '0';
END;
$BODY$ LANGUAGE plpgsql;

-- 测试其他表提示的修复
/*
原始SQL Server代码：
SELECT * FROM table1 WITH(READUNCOMMITTED)
SELECT * FROM table2 WITH(UPDLOCK)
SELECT * FROM table3 WITH(INDEX(idx_name))

修复后的KingBase代码：
SELECT * FROM table1
SELECT * FROM table2  
SELECT * FROM table3
*/

-- 数据类型修复示例
/*
原始SQL Server类型 -> KingBase类型：
VARCHAR(max)     -> TEXT
NVARCHAR(max)    -> TEXT
VARBINARY(max)   -> BYTEA
NTEXT            -> TEXT
*/
