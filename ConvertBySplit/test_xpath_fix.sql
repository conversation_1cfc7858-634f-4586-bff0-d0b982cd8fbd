-- 测试xpath表达式修复的示例

-- 原始错误的转换结果（您遇到的问题）
/*
CREATE OR REPLACE FUNCTION CrmClueMReadSave1(chrXML TEXT) RETURNS VARCHAR(50) AS $BODY$
DECLARE
-- 1新增,2修改
chrFlag CHAR(1);
intLID INTEGER;
chrError VARCHAR(500);
intDoc INTEGER;
BEGIN
    DELETE FROM testt;
    INSERT INTO testt (test) VALUES (chrXML);
    BEGIN
        -- 传入参数解析部分******************************************************************************************

        -- XML处理需要手动实现
        -- 原始: exec sp_xml_preparedocument intDoc, chrXML SELECT
        -- KingBase不支持sp_xml_preparedocument，需要使用XML函数替代
        (xpath('/root/clue/LID/text()', intDoc::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/clue/SalesOrder/text()', intDoc::xml))[1]::text::VARCHAR AS SalesOrder
        FROM (SELECT 1) AS dummy_table;

        -- 注意：以下字段定义应该在CREATE TABLE语句中定义
        -- CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))

        select intLID=LID FROM temp_DataM  ;

        UPDATE a;
        SET;
        SalesOrder = b.SalesOrder,
        a.CustomerInfo = b.CustomerInfo,
        a.LostReason = b.LostReason,
        a.Suggestion = b.Suggestion
        FROM crmClueM a
        INNER JOIN temp_DataM b ON b.LID = a.LID
        WHERE a.LID=intLID END
        RETURN '0'; -- 默认返回值

    END;
    $BODY$ LANGUAGE plpgsql;
*/

-- 期望的修复后结果
CREATE OR REPLACE FUNCTION CrmClueMReadSave1(chrXML TEXT) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    -- 1新增,2修改
    chrFlag CHAR(1);
    intLID INTEGER;
    chrError VARCHAR(500);
BEGIN
    DELETE FROM testt;
    INSERT INTO testt (test) VALUES (chrXML);

    -- 修复的OPENXML转换
    CREATE TEMP TABLE temp_DataM AS
    SELECT
        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,
        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,
        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,
        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;

    -- 获取变量值
    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;

    -- 更新数据
    UPDATE crmClueM
    SET 
        SalesOrder = b.SalesOrder,
        CustomerInfo = b.CustomerInfo,
        LostReason = b.LostReason,
        Suggestion = b.Suggestion
    FROM temp_DataM b
    WHERE crmClueM.LID = b.LID AND crmClueM.LID = intLID;

    RETURN '1'; -- 成功返回
END;
$BODY$ LANGUAGE plpgsql;

-- 测试调用
-- SELECT CrmClueMReadSave1('<root><clue><LID>123</LID><SalesOrder>SO001</SalesOrder><CustomerInfo>Customer Info</CustomerInfo><LostReason>Lost Reason</LostReason><Suggestion>Suggestion</Suggestion></clue></root>');
