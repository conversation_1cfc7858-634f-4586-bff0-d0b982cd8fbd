package com.converter.model;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 转换请求模型
 * 用于接收前端传入的SQL Server存储过程代码
 */
public class ConversionRequest {

    @NotBlank(message = "SQL Server存储过程代码不能为空")
    @Size(max = 100000, message = "存储过程代码长度不能超过100000字符")
    private String sqlServerCode;

    /**
     * 转换选项配置
     */
    private ConversionOptions options;

    public ConversionRequest() {
        this.options = new ConversionOptions();
    }

    public ConversionRequest(String sqlServerCode) {
        this.sqlServerCode = sqlServerCode;
        this.options = new ConversionOptions();
    }

    public String getSqlServerCode() {
        return sqlServerCode;
    }

    public void setSqlServerCode(String sqlServerCode) {
        this.sqlServerCode = sqlServerCode;
    }

    public ConversionOptions getOptions() {
        return options;
    }

    public void setOptions(ConversionOptions options) {
        this.options = options;
    }

    /**
     * 转换选项配置类
     */
    public static class ConversionOptions {
        
        /**
         * 是否保留注释
         */
        private boolean preserveComments = true;
        
        /**
         * 是否格式化输出
         */
        private boolean formatOutput = true;
        
        /**
         * 是否严格模式转换
         */
        private boolean strictMode = false;

        public boolean isPreserveComments() {
            return preserveComments;
        }

        public void setPreserveComments(boolean preserveComments) {
            this.preserveComments = preserveComments;
        }

        public boolean isFormatOutput() {
            return formatOutput;
        }

        public void setFormatOutput(boolean formatOutput) {
            this.formatOutput = formatOutput;
        }

        public boolean isStrictMode() {
            return strictMode;
        }

        public void setStrictMode(boolean strictMode) {
            this.strictMode = strictMode;
        }
    }
}
