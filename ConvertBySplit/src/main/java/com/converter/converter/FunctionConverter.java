package com.converter.converter;

import com.converter.model.ConversionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 函数转换器
 * 负责将SQL Server函数转换为KingBase兼容的函数
 */
@Component
public class FunctionConverter {

    private static final Logger logger = LoggerFactory.getLogger(FunctionConverter.class);

    /**
     * SQL Server到KingBase函数映射表
     */
    private static final Map<String, String> FUNCTION_MAPPING = new HashMap<>();

    static {
        // 字符串函数
        FUNCTION_MAPPING.put("LEN", "LENGTH");
        FUNCTION_MAPPING.put("CHARINDEX", "POSITION");
        FUNCTION_MAPPING.put("PATINDEX", "POSITION");
        FUNCTION_MAPPING.put("STUFF", "OVERLAY");
        FUNCTION_MAPPING.put("ISNULL", "COALESCE");
        FUNCTION_MAPPING.put("NULLIF", "NULLIF");

        // 日期函数
        FUNCTION_MAPPING.put("GETDATE", "NOW");
        FUNCTION_MAPPING.put("GETUTCDATE", "NOW");
        FUNCTION_MAPPING.put("DATEADD", "DATE_ADD");
        FUNCTION_MAPPING.put("DATEDIFF", "DATE_DIFF");
        FUNCTION_MAPPING.put("DATEPART", "EXTRACT");
        FUNCTION_MAPPING.put("YEAR", "EXTRACT");
        FUNCTION_MAPPING.put("MONTH", "EXTRACT");
        FUNCTION_MAPPING.put("DAY", "EXTRACT");

        // 数学函数
        FUNCTION_MAPPING.put("RAND", "RANDOM");
        FUNCTION_MAPPING.put("NEWID", "GEN_RANDOM_UUID");

        // 系统函数
        FUNCTION_MAPPING.put("@@IDENTITY", "LASTVAL");
        FUNCTION_MAPPING.put("@@ROWCOUNT", "ROW_COUNT");
        FUNCTION_MAPPING.put("@@ERROR", "SQLSTATE");

        // 聚合函数
        FUNCTION_MAPPING.put("ISNUMERIC", "IS_NUMERIC");
    }

    /**
     * 转换函数
     *
     * @param code   输入代码
     * @param result 转换结果对象，用于记录警告和错误
     * @return 转换后的代码
     */
    public String convert(String code, ConversionResult result) {
        logger.debug("开始函数转换");

        String convertedCode = code;
        int conversionCount = 0;

        // 转换简单的函数映射
        convertedCode = convertSimpleFunctions(convertedCode, result);

        // 转换特殊的函数
        convertedCode = convertSpecialFunctions(convertedCode, result);

        // 转换日期函数
        convertedCode = convertDateFunctions(convertedCode, result);

        // 转换字符串函数
        convertedCode = convertStringFunctions(convertedCode, result);

        // 转换XML相关函数
        convertedCode = convertXmlFunctions(convertedCode, result);

        // 处理函数声明中的OUTPUT参数
        convertedCode = handleOutputParametersInFunctions(convertedCode, result);

        // 修复常见的语法错误
        convertedCode = fixCommonSyntaxErrors(convertedCode, result);

        logger.debug("函数转换完成，共转换 {} 个函数", conversionCount);
        result.getStatistics().setFunctionConversions(conversionCount);

        return convertedCode;
    }

    /**
     * 转换简单的函数映射
     */
    private String convertSimpleFunctions(String code, ConversionResult result) {
        String convertedCode = code;

        for (Map.Entry<String, String> entry : FUNCTION_MAPPING.entrySet()) {
            String sqlServerFunc = entry.getKey();
            String kingBaseFunc = entry.getValue();

            // 使用正则表达式进行函数名替换，确保是完整的函数调用
            Pattern pattern = Pattern.compile(
                "\\b" + Pattern.quote(sqlServerFunc) + "\\s*\\(",
                Pattern.CASE_INSENSITIVE
            );

            Matcher matcher = pattern.matcher(convertedCode);
            if (matcher.find()) {
                convertedCode = matcher.replaceAll(kingBaseFunc + "(");
                result.addWarning("函数转换: " + sqlServerFunc + " -> " + kingBaseFunc);
            }
        }

        return convertedCode;
    }

    /**
     * 转换特殊函数
     */
    private String convertSpecialFunctions(String code, ConversionResult result) {
        String convertedCode = code;

        // 转换CHARINDEX函数
        convertedCode = convertCharIndexFunction(convertedCode, result);

        // 转换ISNULL函数
        convertedCode = convertIsNullFunction(convertedCode, result);

        // 转换STUFF函数
        convertedCode = convertStuffFunction(convertedCode, result);

        return convertedCode;
    }

    /**
     * 转换CHARINDEX函数
     * CHARINDEX(substring, string [, start_location]) -> POSITION(substring IN string)
     */
    private String convertCharIndexFunction(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "CHARINDEX\\s*\\(\\s*([^,]+)\\s*,\\s*([^,)]+)(?:\\s*,\\s*([^)]+))?\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String substring = matcher.group(1).trim();
            String string = matcher.group(2).trim();
            String startLocation = matcher.group(3);

            String replacement;
            if (startLocation != null) {
                // 如果有起始位置，需要使用SUBSTRING和POSITION组合
                replacement = "POSITION(" + substring + " IN SUBSTRING(" + string + " FROM " + startLocation.trim() + "))";
                result.addWarning("CHARINDEX函数带起始位置的转换可能需要手动调整");
            } else {
                replacement = "POSITION(" + substring + " IN " + string + ")";
            }

            matcher.appendReplacement(sb, replacement);
            result.addWarning("CHARINDEX函数已转换为POSITION函数");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换ISNULL函数
     * ISNULL(expression, replacement) -> COALESCE(expression, replacement)
     */
    private String convertIsNullFunction(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "ISNULL\\s*\\(\\s*([^,]+)\\s*,\\s*([^)]+)\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String expression = matcher.group(1).trim();
            String replacement = matcher.group(2).trim();

            String newFunction = "COALESCE(" + expression + ", " + replacement + ")";
            matcher.appendReplacement(sb, newFunction);
            result.addWarning("ISNULL函数已转换为COALESCE函数");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换STUFF函数
     * STUFF(string, start, length, replacement) -> OVERLAY(string PLACING replacement FROM start FOR length)
     */
    private String convertStuffFunction(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "STUFF\\s*\\(\\s*([^,]+)\\s*,\\s*([^,]+)\\s*,\\s*([^,]+)\\s*,\\s*([^)]+)\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String string = matcher.group(1).trim();
            String start = matcher.group(2).trim();
            String length = matcher.group(3).trim();
            String replacement = matcher.group(4).trim();

            String newFunction = "OVERLAY(" + string + " PLACING " + replacement + " FROM " + start + " FOR " + length + ")";
            matcher.appendReplacement(sb, newFunction);
            result.addWarning("STUFF函数已转换为OVERLAY函数");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换日期函数
     */
    private String convertDateFunctions(String code, ConversionResult result) {
        String convertedCode = code;

        // 转换GETDATE()为NOW()
        convertedCode = convertedCode.replaceAll(
            "(?i)\\bGETDATE\\s*\\(\\s*\\)",
            "NOW()"
        );

        // 转换DATEPART函数
        convertedCode = convertDatePartFunction(convertedCode, result);

        // 转换DATEADD函数
        convertedCode = convertDateAddFunction(convertedCode, result);

        // 转换DATEDIFF函数
        convertedCode = convertDateDiffFunction(convertedCode, result);

        return convertedCode;
    }

    /**
     * 转换DATEPART函数
     * DATEPART(datepart, date) -> EXTRACT(datepart FROM date)
     */
    private String convertDatePartFunction(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "DATEPART\\s*\\(\\s*(\\w+)\\s*,\\s*([^)]+)\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String datePart = matcher.group(1).trim().toLowerCase();
            String date = matcher.group(2).trim();

            // 映射日期部分名称
            String extractPart = mapDatePart(datePart);
            
            String replacement = "EXTRACT(" + extractPart + " FROM " + date + ")";
            matcher.appendReplacement(sb, replacement);
            result.addWarning("DATEPART函数已转换为EXTRACT函数");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 映射日期部分名称
     */
    private String mapDatePart(String sqlServerDatePart) {
        switch (sqlServerDatePart.toLowerCase()) {
            case "yy":
            case "yyyy":
                return "YEAR";
            case "mm":
                return "MONTH";
            case "dd":
                return "DAY";
            case "hh":
                return "HOUR";
            case "mi":
                return "MINUTE";
            case "ss":
                return "SECOND";
            case "dw":
                return "DOW";
            case "dy":
                return "DOY";
            case "wk":
            case "ww":
                return "WEEK";
            default:
                return sqlServerDatePart.toUpperCase();
        }
    }

    /**
     * 转换DATEADD函数
     */
    private String convertDateAddFunction(String code, ConversionResult result) {
        // DATEADD在KingBase中可能需要使用不同的语法
        // 这里提供基本转换，实际使用时可能需要根据具体情况调整
        result.addWarning("DATEADD函数转换可能需要手动调整语法");
        return code;
    }

    /**
     * 转换DATEDIFF函数
     */
    private String convertDateDiffFunction(String code, ConversionResult result) {
        // DATEDIFF在KingBase中可能需要使用不同的语法
        // 这里提供基本转换，实际使用时可能需要根据具体情况调整
        result.addWarning("DATEDIFF函数转换可能需要手动调整语法");
        return code;
    }

    /**
     * 转换字符串函数
     */
    private String convertStringFunctions(String code, ConversionResult result) {
        String convertedCode = code;

        // 转换LEN函数为LENGTH函数
        convertedCode = convertedCode.replaceAll(
            "(?i)\\bLEN\\s*\\(",
            "LENGTH("
        );

        return convertedCode;
    }

    /**
     * 处理函数声明中的OUTPUT参数
     * 在KingBase中，OUTPUT参数不能出现在函数参数列表中，需要特殊处理
     */
    private String handleOutputParametersInFunctions(String code, ConversionResult result) {
        String convertedCode = code;

        // 匹配函数声明中的OUTPUT参数
        // 模式: CREATE OR REPLACE FUNCTION name(...参数 OUTPUT...) RETURNS
        Pattern functionPattern = Pattern.compile(
            "(CREATE\\s+(?:OR\\s+REPLACE\\s+)?FUNCTION\\s+\\w+\\s*\\()([^)]*)(\\)\\s*RETURNS)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = functionPattern.matcher(convertedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String functionStart = matcher.group(1);
            String parameters = matcher.group(2);
            String functionEnd = matcher.group(3);

            // 处理参数列表中的OUTPUT关键字
            String cleanedParameters = cleanOutputFromParameters(parameters, result);

            String replacement = functionStart + cleanedParameters + functionEnd;
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 从参数列表中清理OUTPUT关键字
     */
    private String cleanOutputFromParameters(String parameters, ConversionResult result) {
        if (parameters == null || parameters.trim().isEmpty()) {
            return parameters;
        }

        String cleaned = parameters;

        // 移除OUTPUT关键字及其周围的空白和逗号
        cleaned = cleaned.replaceAll("(?i)\\s*OUTPUT\\s*,?", "");
        cleaned = cleaned.replaceAll("(?i),\\s*OUTPUT\\s*", "");

        // 清理多余的逗号和空白
        cleaned = cleaned.replaceAll(",\\s*,", ",");
        cleaned = cleaned.replaceAll("^\\s*,\\s*", "");
        cleaned = cleaned.replaceAll("\\s*,\\s*$", "");
        cleaned = cleaned.trim();

        // 如果发现并处理了OUTPUT参数，记录警告
        if (!cleaned.equals(parameters.trim())) {
            result.addWarning("函数参数列表中的OUTPUT关键字已移除，KingBase不支持在函数参数中使用OUTPUT");
        }

        return cleaned;
    }

    /**
     * 转换XML相关函数
     * 处理OPENXML和其他XML函数的转换
     */
    private String convertXmlFunctions(String code, ConversionResult result) {
        String convertedCode = code;

        // 转换OPENXML函数调用
        convertedCode = convertOpenXmlFunction(convertedCode, result);

        // 转换XML.value()函数
        convertedCode = convertXmlValueFunction(convertedCode, result);

        // 转换XML.query()函数
        convertedCode = convertXmlQueryFunction(convertedCode, result);

        return convertedCode;
    }

    /**
     * 转换OPENXML函数为KingBase兼容的xpath函数
     */
    private String convertOpenXmlFunction(String code, ConversionResult result) {
        // 匹配CREATE TEMP TABLE ... AS SELECT ... FROM openxml(...) with (...)
        Pattern openxmlPattern = Pattern.compile(
            "(CREATE\\s+TEMP\\s+TABLE\\s+\\w+\\s+AS\\s+)?SELECT\\s+([\\s\\S]*?)\\s+FROM\\s+openxml\\s*\\(([^)]+)\\)\\s*with\\s*\\(([\\s\\S]*?)\\)(?=\\s*[;,]|\\s*$)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = openxmlPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String createTablePrefix = matcher.group(1);
            String selectClause = matcher.group(2).trim();
            String openxmlParams = matcher.group(3).trim();
            String withClause = matcher.group(4).trim();

            // 解析openxml参数
            String[] params = openxmlParams.split(",");
            String xmlDoc = params.length > 0 ? params[0].trim() : "xml_doc";
            String xpath = params.length > 1 ? params[1].trim().replaceAll("'", "") : "/root";

            // 构建KingBase兼容的查询
            StringBuilder replacement = new StringBuilder();

            if (createTablePrefix != null) {
                replacement.append(createTablePrefix);
            }

            replacement.append("SELECT\n");

            // 解析WITH子句中的列定义
            String[] columns = parseWithClause(withClause);

            for (int i = 0; i < columns.length; i++) {
                String column = columns[i].trim();
                String[] parts = column.split("\\s+", 2);
                if (parts.length >= 2) {
                    String columnName = parts[0].trim();
                    String dataType = parts[1].trim();

                    // 构建xpath表达式
                    String xpathExpr = xpath + "/" + columnName;

                    replacement.append("        (xpath('").append(xpathExpr).append("/text()', ").append(xmlDoc).append("::xml))[1]::text");

                    // 添加数据类型转换
                    if (dataType.toUpperCase().startsWith("INT")) {
                        replacement.append("::INTEGER");
                    } else if (dataType.toUpperCase().contains("VARCHAR")) {
                        replacement.append("::VARCHAR");
                    } else if (dataType.toUpperCase().startsWith("CHAR")) {
                        replacement.append("::CHAR");
                    } else if (dataType.toUpperCase().contains("NUMERIC")) {
                        replacement.append("::NUMERIC");
                    }

                    replacement.append(" AS ").append(columnName);

                    if (i < columns.length - 1) {
                        replacement.append(",\n");
                    }
                }
            }

            replacement.append("\n        FROM (SELECT 1) AS dummy_table");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("OPENXML函数已转换为KingBase的xpath函数，请验证XML路径和数据类型");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 解析WITH子句中的列定义
     */
    private String[] parseWithClause(String withClause) {
        if (withClause == null || withClause.trim().isEmpty()) {
            return new String[0];
        }

        // 移除多余的空白和换行
        String cleaned = withClause.replaceAll("\\s+", " ").trim();

        // 简单的逗号分割，后续可以改进为更复杂的解析
        String[] parts = cleaned.split(",");

        // 清理每个部分
        for (int i = 0; i < parts.length; i++) {
            parts[i] = parts[i].trim();
        }

        return parts;
    }

    /**
     * 转换XML.value()函数
     */
    private String convertXmlValueFunction(String code, ConversionResult result) {
        // XML.value('xpath', 'datatype') -> (xpath('xpath', xml_column))[1]::datatype
        Pattern xmlValuePattern = Pattern.compile(
            "(\\w+)\\.value\\s*\\(\\s*'([^']+)'\\s*,\\s*'([^']+)'\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = xmlValuePattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String xmlColumn = matcher.group(1);
            String xpathExpr = matcher.group(2);
            String dataType = matcher.group(3);

            String replacement = "(xpath('" + xpathExpr + "', " + xmlColumn + "::xml))[1]::text::" + dataType.toLowerCase();

            matcher.appendReplacement(sb, replacement);
            result.addWarning("XML.value()函数已转换为xpath函数");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换XML.query()函数
     */
    private String convertXmlQueryFunction(String code, ConversionResult result) {
        // XML.query('xpath') -> xpath('xpath', xml_column)
        Pattern xmlQueryPattern = Pattern.compile(
            "(\\w+)\\.query\\s*\\(\\s*'([^']+)'\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = xmlQueryPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String xmlColumn = matcher.group(1);
            String xpathExpr = matcher.group(2);

            String replacement = "xpath('" + xpathExpr + "', " + xmlColumn + "::xml)";

            matcher.appendReplacement(sb, replacement);
            result.addWarning("XML.query()函数已转换为xpath函数");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 修复常见的语法错误
     */
    private String fixCommonSyntaxErrors(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复多余的分号（如 AS column,; 的情况）
        fixedCode = fixedCode.replaceAll(",\\s*;", ";");

        // 修复SELECT语句中的多余逗号
        fixedCode = fixedCode.replaceAll(",\\s*FROM", " FROM");
        fixedCode = fixedCode.replaceAll(",\\s*WHERE", " WHERE");
        fixedCode = fixedCode.replaceAll(",\\s*ORDER", " ORDER");
        fixedCode = fixedCode.replaceAll(",\\s*GROUP", " GROUP");

        // 修复多余的逗号在行尾
        fixedCode = fixedCode.replaceAll(",\\s*\\n\\s*FROM", "\n        FROM");
        fixedCode = fixedCode.replaceAll(",\\s*\\n\\s*WHERE", "\n        WHERE");

        // 修复CREATE TEMP TABLE语句中的语法错误
        fixedCode = fixCreateTempTableSyntax(fixedCode, result);

        // 修复混合注释和代码的问题
        fixedCode = fixMixedCommentsAndCode(fixedCode, result);

        // 如果进行了修复，记录警告
        if (!fixedCode.equals(code)) {
            result.addWarning("已修复常见的SQL语法错误（多余的逗号和分号）");
        }

        return fixedCode;
    }

    /**
     * 修复CREATE TEMP TABLE语句中的语法错误
     */
    private String fixCreateTempTableSyntax(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复SELECT语句中多余的分号和逗号
        // 例如: AS LID,; -> AS LID,
        Pattern asCommaPattern = Pattern.compile("(AS\\s+\\w+)\\s*,\\s*;", Pattern.CASE_INSENSITIVE);
        Matcher asMatcher = asCommaPattern.matcher(fixedCode);
        if (asMatcher.find()) {
            fixedCode = asMatcher.replaceAll("$1,");
            result.addWarning("修复了AS子句中的多余分号");
        }

        // 修复错误放置的字段定义
        fixedCode = fixMisplacedColumnDefinitions(fixedCode, result);

        return fixedCode;
    }

    /**
     * 修复错误放置的字段定义
     */
    private String fixMisplacedColumnDefinitions(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找错误放置的字段定义模式
        // 匹配: FROM ... AS dummy_table, 后面跟着字段定义
        Pattern misplacedPattern = Pattern.compile(
            "(FROM\\s+\\([^)]+\\)\\s+AS\\s+\\w+)\\s*,\\s*([\\s\\S]*?)(?=\\n\\s*(?:--|\\/\\*|SELECT|FROM|WHERE|ORDER|GROUP|$))",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = misplacedPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String fromClause = matcher.group(1);
            String columnDefinitions = matcher.group(2);

            // 检查是否包含字段定义
            if (isColumnDefinition(columnDefinitions)) {
                // 将字段定义注释掉
                String commentedDefinitions = commentOutColumnDefinitions(columnDefinitions);

                String replacement = fromClause + ";\n\n" +
                    "-- 注意：以下字段定义应该在CREATE TABLE语句中定义\n" +
                    commentedDefinitions;

                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
                result.addWarning("修复了错误放置的字段定义，已将其注释并提示正确位置");
            } else {
                // 不是字段定义，保持原样
                matcher.appendReplacement(sb, matcher.group(0));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 检查文本是否包含字段定义
     */
    private boolean isColumnDefinition(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含数据类型关键字
        String upperText = text.toUpperCase();
        return upperText.contains("VARCHAR") ||
               upperText.contains("CHAR") ||
               upperText.contains("INT") ||
               upperText.contains("NUMERIC") ||
               upperText.contains("DECIMAL") ||
               upperText.contains("DATE") ||
               upperText.contains("TEXT");
    }

    /**
     * 将字段定义注释掉
     */
    private String commentOutColumnDefinitions(String columnDefinitions) {
        StringBuilder sb = new StringBuilder();
        String[] lines = columnDefinitions.split("\\n");

        for (String line : lines) {
            String trimmedLine = line.trim();
            if (!trimmedLine.isEmpty()) {
                // 如果行不是以注释开头，添加注释标记
                if (!trimmedLine.startsWith("--")) {
                    sb.append("-- ").append(trimmedLine).append("\n");
                } else {
                    sb.append(trimmedLine).append("\n");
                }
            }
        }

        return sb.toString();
    }

    /**
     * 修复混合注释和代码的问题
     */
    private String fixMixedCommentsAndCode(String code, ConversionResult result) {
        String fixedCode = code;

        // 处理以 /* 开头但没有结束的注释，且后面跟着实际代码
        Pattern mixedPattern = Pattern.compile(
            "/\\*\\s*([^*]*(?:\\*(?!/)[^*]*)*)\\s*(CREATE\\s+TEMP\\s+TABLE|SELECT|INSERT|UPDATE|DELETE)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = mixedPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String commentPart = matcher.group(1);
            String codePart = matcher.group(2);

            StringBuilder replacement = new StringBuilder();

            // 将注释部分转换为单行注释
            if (commentPart != null && !commentPart.trim().isEmpty()) {
                String[] lines = commentPart.split("\\n");
                for (String line : lines) {
                    String trimmedLine = line.trim();
                    if (!trimmedLine.isEmpty()) {
                        replacement.append("-- ").append(trimmedLine).append("\n");
                    }
                }
            }

            // 添加代码部分
            replacement.append(codePart);

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("修复了混合的注释和代码结构");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }
}
