package com.converter.converter;

import com.converter.model.ConversionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据类型转换器
 * 负责将SQL Server数据类型转换为KingBase兼容的数据类型
 */
@Component
public class DataTypeConverter {

    private static final Logger logger = LoggerFactory.getLogger(DataTypeConverter.class);

    /**
     * SQL Server到KingBase数据类型映射表
     */
    private static final Map<String, String> DATA_TYPE_MAPPING = new HashMap<>();

    static {
        // 字符串类型
        DATA_TYPE_MAPPING.put("VARCHAR", "VARCHAR");
        DATA_TYPE_MAPPING.put("NVARCHAR", "VARCHAR");
        DATA_TYPE_MAPPING.put("CHAR", "CHAR");
        DATA_TYPE_MAPPING.put("NCHAR", "CHAR");
        DATA_TYPE_MAPPING.put("TEXT", "TEXT");
        DATA_TYPE_MAPPING.put("NTEXT", "TEXT");

        // 数值类型
        DATA_TYPE_MAPPING.put("INT", "INTEGER");
        DATA_TYPE_MAPPING.put("INTEGER", "INTEGER");
        DATA_TYPE_MAPPING.put("BIGINT", "BIGINT");
        DATA_TYPE_MAPPING.put("SMALLINT", "SMALLINT");
        DATA_TYPE_MAPPING.put("TINYINT", "SMALLINT");
        DATA_TYPE_MAPPING.put("BIT", "BOOLEAN");
        DATA_TYPE_MAPPING.put("DECIMAL", "DECIMAL");
        DATA_TYPE_MAPPING.put("NUMERIC", "NUMERIC");
        DATA_TYPE_MAPPING.put("FLOAT", "DOUBLE PRECISION");
        DATA_TYPE_MAPPING.put("REAL", "REAL");
        DATA_TYPE_MAPPING.put("MONEY", "DECIMAL(19,4)");
        DATA_TYPE_MAPPING.put("SMALLMONEY", "DECIMAL(10,4)");

        // 日期时间类型
        DATA_TYPE_MAPPING.put("DATETIME", "TIMESTAMP");
        DATA_TYPE_MAPPING.put("DATETIME2", "TIMESTAMP");
        DATA_TYPE_MAPPING.put("SMALLDATETIME", "TIMESTAMP");
        DATA_TYPE_MAPPING.put("DATE", "DATE");
        DATA_TYPE_MAPPING.put("TIME", "TIME");
        DATA_TYPE_MAPPING.put("TIMESTAMP", "BYTEA");

        // 二进制类型
        DATA_TYPE_MAPPING.put("BINARY", "BYTEA");
        DATA_TYPE_MAPPING.put("VARBINARY", "BYTEA");
        DATA_TYPE_MAPPING.put("IMAGE", "BYTEA");

        // 其他类型
        DATA_TYPE_MAPPING.put("UNIQUEIDENTIFIER", "UUID");
        DATA_TYPE_MAPPING.put("XML", "XML");
        DATA_TYPE_MAPPING.put("SQL_VARIANT", "TEXT");
    }

    /**
     * 转换数据类型
     *
     * @param code   输入代码
     * @param result 转换结果对象，用于记录警告和错误
     * @return 转换后的代码
     */
    public String convert(String code, ConversionResult result) {
        logger.debug("开始数据类型转换");

        String convertedCode = code;
        int conversionCount = 0;

        // 转换参数声明中的数据类型
        convertedCode = convertParameterDataTypes(convertedCode, result);

        // 转换变量声明中的数据类型
        convertedCode = convertVariableDataTypes(convertedCode, result);

        // 转换表创建语句中的数据类型
        convertedCode = convertTableDataTypes(convertedCode, result);

        // 转换CAST和CONVERT函数中的数据类型
        convertedCode = convertCastDataTypes(convertedCode, result);

        logger.debug("数据类型转换完成，共转换 {} 个数据类型", conversionCount);
        result.getStatistics().setDataTypeConversions(conversionCount);

        return convertedCode;
    }

    /**
     * 转换参数声明中的数据类型
     */
    private String convertParameterDataTypes(String code, ConversionResult result) {
        // 匹配参数声明模式：@参数名 数据类型
        Pattern pattern = Pattern.compile(
            "@(\\w+)\\s+(\\w+)(?:\\s*\\((\\d+(?:,\\s*\\d+)?)\\))?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String paramName = matcher.group(1);
            String dataType = matcher.group(2).toUpperCase();
            String precision = matcher.group(3);

            String kingBaseType = convertSingleDataType(dataType, precision, result);
            
            String replacement = paramName + " " + kingBaseType;
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换变量声明中的数据类型
     */
    private String convertVariableDataTypes(String code, ConversionResult result) {
        String convertedCode = code;

        // 首先处理VARCHAR(max)等特殊情况
        convertedCode = handleSpecialDataTypes(convertedCode, result);

        // 匹配DECLARE语句中的数据类型
        Pattern pattern = Pattern.compile(
            "DECLARE\\s+@(\\w+)\\s+(\\w+)(?:\\s*\\((\\d+(?:,\\s*\\d+)?)\\))?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(convertedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String varName = matcher.group(1);
            String dataType = matcher.group(2).toUpperCase();
            String precision = matcher.group(3);

            String kingBaseType = convertSingleDataType(dataType, precision, result);

            String replacement = "DECLARE " + varName + " " + kingBaseType;
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换表创建语句中的数据类型
     */
    private String convertTableDataTypes(String code, ConversionResult result) {
        // 匹配CREATE TABLE语句中的列定义
        Pattern pattern = Pattern.compile(
            "(\\w+)\\s+(\\w+)(?:\\s*\\((\\d+(?:,\\s*\\d+)?)\\))?",
            Pattern.CASE_INSENSITIVE
        );

        return code; // 暂时返回原代码，后续可以扩展
    }

    /**
     * 转换CAST和CONVERT函数中的数据类型
     */
    private String convertCastDataTypes(String code, ConversionResult result) {
        // 转换CAST函数
        Pattern castPattern = Pattern.compile(
            "CAST\\s*\\(([^)]+)\\s+AS\\s+(\\w+)(?:\\s*\\((\\d+(?:,\\s*\\d+)?)\\))?\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher castMatcher = castPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (castMatcher.find()) {
            String expression = castMatcher.group(1);
            String dataType = castMatcher.group(2).toUpperCase();
            String precision = castMatcher.group(3);

            String kingBaseType = convertSingleDataType(dataType, precision, result);
            
            String replacement = "CAST(" + expression + " AS " + kingBaseType + ")";
            castMatcher.appendReplacement(sb, replacement);
        }
        castMatcher.appendTail(sb);

        // 转换CONVERT函数为CAST函数
        String convertedCode = sb.toString();
        Pattern convertPattern = Pattern.compile(
            "CONVERT\\s*\\((\\w+)(?:\\s*\\((\\d+(?:,\\s*\\d+)?)\\))?\\s*,\\s*([^)]+)\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher convertMatcher = convertPattern.matcher(convertedCode);
        StringBuffer sb2 = new StringBuffer();

        while (convertMatcher.find()) {
            String dataType = convertMatcher.group(1).toUpperCase();
            String precision = convertMatcher.group(2);
            String expression = convertMatcher.group(3);

            String kingBaseType = convertSingleDataType(dataType, precision, result);
            
            String replacement = "CAST(" + expression + " AS " + kingBaseType + ")";
            convertMatcher.appendReplacement(sb2, replacement);
            
            result.addWarning("CONVERT函数已转换为CAST函数: " + convertMatcher.group(0));
        }
        convertMatcher.appendTail(sb2);

        return sb2.toString();
    }

    /**
     * 转换单个数据类型
     */
    private String convertSingleDataType(String sqlServerType, String precision, ConversionResult result) {
        String upperType = sqlServerType.toUpperCase();
        String kingBaseType = DATA_TYPE_MAPPING.get(upperType);

        if (kingBaseType == null) {
            result.addWarning("未知的数据类型: " + sqlServerType + "，保持原样");
            return sqlServerType + (precision != null ? "(" + precision + ")" : "");
        }

        // 处理精度
        if (precision != null && !kingBaseType.contains("(")) {
            // 某些类型需要保留精度
            if (kingBaseType.equals("VARCHAR") || kingBaseType.equals("CHAR") || 
                kingBaseType.equals("DECIMAL") || kingBaseType.equals("NUMERIC")) {
                kingBaseType += "(" + precision + ")";
            }
        }

        if (!upperType.equals(kingBaseType)) {
            result.addWarning("数据类型转换: " + sqlServerType + " -> " + kingBaseType);
        }

        return kingBaseType;
    }
}
