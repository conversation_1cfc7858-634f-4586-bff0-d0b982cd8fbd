package com.converter.converter;

import com.converter.model.ConversionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 语法转换器
 * 负责将SQL Server语法结构转换为KingBase兼容的语法
 */
@Component
public class SyntaxConverter {

    private static final Logger logger = LoggerFactory.getLogger(SyntaxConverter.class);

    /**
     * 转换语法结构
     *
     * @param code   输入代码
     * @param result 转换结果对象，用于记录警告和错误
     * @return 转换后的代码
     */
    public String convert(String code, ConversionResult result) {
        logger.debug("开始语法结构转换");

        String convertedCode = code;
        int conversionCount = 0;

        // 首先移除SQL Server特有的语句（这些不需要转换，直接移除）
        convertedCode = removeGoStatements(convertedCode, result);
        convertedCode = removeUseStatements(convertedCode, result);
        convertedCode = removeSqlServerSetStatements(convertedCode, result);

        // 立即修复孤立的xpath表达式（在其他转换之前）
        convertedCode = immediateFixOrphanXpath(convertedCode, result);

        // 处理完整的OPENXML块（在其他转换之前）
        convertedCode = convertCompleteOpenXmlBlock(convertedCode, result);

        // 强制修复已经转换错误的OPENXML代码
        convertedCode = forceFixBrokenOpenXml(convertedCode, result);

        // 专门处理您的原始OPENXML模式（最优先）
        convertedCode = fixCompleteOpenXmlBlockEarly(convertedCode, result);

        // 最终的强制修复方法
        convertedCode = ultimateOpenXmlFix(convertedCode, result);

        // 转换存储过程声明
        convertedCode = convertProcedureDeclaration(convertedCode, result);

        // 修复常见的转换错误
        convertedCode = fixCommonConversionErrors(convertedCode, result);

        // 转换函数声明
        convertedCode = convertFunctionDeclaration(convertedCode, result);

        // 修复函数结尾
        convertedCode = fixFunctionEndings(convertedCode, result);

        // 转换变量声明
        convertedCode = convertVariableDeclarations(convertedCode, result);

        // 转换变量引用（移除@符号）
        convertedCode = convertVariableReferences(convertedCode, result);

        // 转换临时表语法
        convertedCode = convertTemporaryTables(convertedCode, result);

        // 转换EXEC语句
        convertedCode = convertExecStatements(convertedCode, result);

        // 转换OPENXML语句
        convertedCode = convertOpenXmlStatements(convertedCode, result);

        // 转换SELECT INTO语句
        convertedCode = convertSelectIntoStatements(convertedCode, result);

        // 转换DELETE语句
        convertedCode = convertDeleteStatements(convertedCode, result);

        // 转换INSERT语句
        convertedCode = convertInsertStatements(convertedCode, result);

        // 添加语句分隔符
        convertedCode = addStatementSeparators(convertedCode, result);

        // 转换IF语句
        convertedCode = convertIfStatements(convertedCode, result);

        // 转换WHILE循环
        convertedCode = convertWhileLoops(convertedCode, result);

        // 转换TRY-CATCH语句
        convertedCode = convertTryCatchStatements(convertedCode, result);

        // 转换OUTPUT参数
        convertedCode = convertOutputParameters(convertedCode, result);

        // 转换SET语句
        convertedCode = convertSetStatements(convertedCode, result);

        // 转换PRINT语句
        convertedCode = convertPrintStatements(convertedCode, result);

        // 转换事务语句
        convertedCode = convertTransactionStatements(convertedCode, result);

        // 转换注释
        convertedCode = convertComments(convertedCode, result);

        // 清理方括号语法
        convertedCode = cleanBracketSyntax(convertedCode, result);

        logger.debug("语法结构转换完成，共转换 {} 个语法结构", conversionCount);
        result.getStatistics().setSyntaxConversions(conversionCount);

        return convertedCode;
    }

    /**
     * 移除USE语句
     * USE [database] -> (移除)
     */
    private String removeUseStatements(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "USE\\s+\\[?\\w+\\]?\\s*;?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        if (matcher.find()) {
            result.addWarning("USE语句已移除，KingBase不支持此语法");
        }

        return matcher.replaceAll("");
    }

    /**
     * 移除GO语句
     * GO -> (移除)
     */
    private String removeGoStatements(String code, ConversionResult result) {
        String convertedCode = code;

        // 匹配各种形式的GO语句
        Pattern[] goPatterns = {
            // 单独一行的GO
            Pattern.compile("^\\s*GO\\s*$", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE),
            // 行末的GO
            Pattern.compile("\\s+GO\\s*$", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE),
            // 行首的GO
            Pattern.compile("^GO\\s*", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE),
            // 任何位置的GO（作为独立单词）
            Pattern.compile("\\bGO\\b", Pattern.CASE_INSENSITIVE)
        };

        boolean foundGo = false;
        for (Pattern pattern : goPatterns) {
            Matcher matcher = pattern.matcher(convertedCode);
            if (matcher.find()) {
                convertedCode = matcher.replaceAll("");
                foundGo = true;
            }
        }

        if (foundGo) {
            result.addWarning("GO语句已移除，KingBase不支持批处理分隔符");
        }

        // 清理多余的空行
        convertedCode = convertedCode.replaceAll("\\n\\s*\\n\\s*\\n", "\n\n");

        return convertedCode;
    }

    /**
     * 移除SQL Server特有的SET语句
     * SET ANSI_NULLS, SET QUOTED_IDENTIFIER 等
     */
    private String removeSqlServerSetStatements(String code, ConversionResult result) {
        String convertedCode = code;

        // 移除SET ANSI_NULLS
        Pattern ansiNullsPattern = Pattern.compile(
            "SET\\s+ANSI_NULLS\\s+(ON|OFF)\\s*;?",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );
        if (ansiNullsPattern.matcher(convertedCode).find()) {
            convertedCode = ansiNullsPattern.matcher(convertedCode).replaceAll("");
            result.addWarning("SET ANSI_NULLS语句已移除");
        }

        // 移除SET QUOTED_IDENTIFIER
        Pattern quotedIdPattern = Pattern.compile(
            "SET\\s+QUOTED_IDENTIFIER\\s+(ON|OFF)\\s*;?",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );
        if (quotedIdPattern.matcher(convertedCode).find()) {
            convertedCode = quotedIdPattern.matcher(convertedCode).replaceAll("");
            result.addWarning("SET QUOTED_IDENTIFIER语句已移除");
        }

        // 移除SET NOCOUNT
        Pattern nocountPattern = Pattern.compile(
            "SET\\s+NOCOUNT\\s+(ON|OFF)\\s*;?",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );
        if (nocountPattern.matcher(convertedCode).find()) {
            convertedCode = nocountPattern.matcher(convertedCode).replaceAll("");
            result.addWarning("SET NOCOUNT语句已移除");
        }

        return convertedCode;
    }

    /**
     * 转换存储过程声明
     * CREATE PROCEDURE -> CREATE OR REPLACE FUNCTION
     */
    private String convertProcedureDeclaration(String code, ConversionResult result) {
        String convertedCode = code;

        // 使用更简单的方法：先尝试匹配标准格式，然后处理特殊格式
        convertedCode = convertStandardProcedure(convertedCode, result);
        convertedCode = convertMultilineProcedure(convertedCode, result);

        // 然后处理简单的ALTER PROCEDURE声明（没有AS BEGIN）
        Pattern simplePattern = Pattern.compile(
            "ALTER\\s+PROCEDURE\\s+(?:\\[?\\w+\\]?\\.)?\\[?(\\w+)\\]?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher simpleMatcher = simplePattern.matcher(convertedCode);
        if (simpleMatcher.find()) {
            convertedCode = simpleMatcher.replaceAll("CREATE OR REPLACE FUNCTION \\$1() RETURNS VOID AS \\\\\\$BODY\\\\\\$");
            result.addWarning("ALTER PROCEDURE已转换为CREATE OR REPLACE FUNCTION");
        }

        // 转换结束语句（如果还没有正确的结束标记）
        if (convertedCode.contains("CREATE OR REPLACE FUNCTION") && !convertedCode.contains("$BODY$ LANGUAGE plpgsql;")) {
            Pattern endPattern = Pattern.compile("(?i)\\bEND\\s*$");
            Matcher endMatcher = endPattern.matcher(convertedCode);
            if (endMatcher.find()) {
                convertedCode = endMatcher.replaceAll("END;\n\\$BODY\\$ LANGUAGE plpgsql;");
            }
        }

        return convertedCode;
    }

    /**
     * 转换标准格式的存储过程（带括号）
     */
    private String convertStandardProcedure(String code, ConversionResult result) {
        // 简单的字符串替换方法，避免复杂的正则表达式
        if (code.toUpperCase().contains("ALTER PROCEDURE") || code.toUpperCase().contains("CREATE PROCEDURE")) {
            // 查找过程名
            String[] lines = code.split("\\n");
            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.toUpperCase().startsWith("ALTER PROCEDURE") || line.toUpperCase().startsWith("CREATE PROCEDURE")) {
                    // 提取过程名
                    String procName = extractProcedureName(line);
                    if (procName != null) {
                        // 查找参数和AS BEGIN
                        StringBuilder params = new StringBuilder();
                        String returnType = "VOID"; // 默认返回类型
                        int asBeginIndex = -1;

                        for (int j = i + 1; j < lines.length; j++) {
                            String paramLine = lines[j].trim();
                            if (paramLine.toUpperCase().equals("AS") || paramLine.toUpperCase().startsWith("AS ")) {
                                asBeginIndex = j;
                                break;
                            }
                            if (paramLine.startsWith("@")) {
                                String processedParam = processParameterLine(paramLine, result);

                                // 检查是否是OUTPUT参数，如果是，设置返回类型
                                if (paramLine.toUpperCase().contains("OUTPUT")) {
                                    String outputType = extractOutputParameterType(paramLine);
                                    if (outputType != null) {
                                        returnType = convertParameterDataType(outputType);
                                        result.getStatistics().setHasOutputParameter(true);
                                        result.getStatistics().setOutputParameterType(outputType);
                                    }
                                } else if (!processedParam.isEmpty()) {
                                    // 只有非OUTPUT参数才添加到参数列表
                                    if (params.length() > 0) params.append(", ");
                                    params.append(processedParam);
                                }
                            }
                        }

                        if (asBeginIndex != -1) {
                            // 构建新的函数声明
                            StringBuilder newDeclaration = new StringBuilder();
                            newDeclaration.append("CREATE OR REPLACE FUNCTION ").append(procName).append("(");
                            newDeclaration.append(params.toString());
                            newDeclaration.append(") RETURNS ").append(returnType).append(" AS $BODY$");

                            // 替换原始声明
                            StringBuilder newCode = new StringBuilder();
                            newCode.append(newDeclaration.toString()).append("\n");

                            // 添加BEGIN和后续内容，同时处理END语句
                            boolean foundEnd = false;
                            for (int k = asBeginIndex + 1; k < lines.length; k++) {
                                String currentLine = lines[k].trim();
                                if (currentLine.toUpperCase().equals("BEGIN")) {
                                    newCode.append("BEGIN\n");
                                } else if (currentLine.toUpperCase().equals("END")) {
                                    newCode.append("END;\n");
                                    newCode.append("$BODY$ LANGUAGE plpgsql;\n");
                                    foundEnd = true;
                                    break;
                                } else {
                                    newCode.append(lines[k]).append("\n");
                                }
                            }

                            // 如果没有找到END语句，添加默认结束
                            if (!foundEnd) {
                                newCode.append("END;\n");
                                newCode.append("$BODY$ LANGUAGE plpgsql;\n");
                            }

                            // 确保函数定义以分号结尾
                            String finalCode = newCode.toString();
                            if (!finalCode.trim().endsWith(";")) {
                                finalCode = finalCode.trim() + ";\n";
                            }

                            result.addWarning("存储过程已转换为函数: " + procName + "，返回类型: " + returnType);
                            return finalCode;
                        }
                    }
                    break;
                }
            }
        }
        return code;
    }

    /**
     * 从OUTPUT参数中提取数据类型
     */
    private String extractOutputParameterType(String paramLine) {
        String[] parts = paramLine.split("\\s+");
        for (int i = 0; i < parts.length; i++) {
            if (parts[i].toUpperCase().equals("OUTPUT") && i > 0) {
                return parts[i-1];
            }
        }
        return null;
    }

    /**
     * 转换多行格式的存储过程
     */
    private String convertMultilineProcedure(String code, ConversionResult result) {
        // 如果已经转换过，跳过
        if (code.contains("CREATE OR REPLACE FUNCTION")) {
            return code;
        }
        return code;
    }

    /**
     * 提取存储过程名称
     */
    private String extractProcedureName(String line) {
        String[] parts = line.split("\\s+");
        for (int i = 0; i < parts.length - 1; i++) {
            if (parts[i].toUpperCase().equals("PROCEDURE")) {
                String name = parts[i + 1];
                // 移除schema前缀和方括号
                if (name.contains(".")) {
                    name = name.substring(name.lastIndexOf(".") + 1);
                }
                name = name.replaceAll("[\\[\\]]", "");
                return name;
            }
        }
        return null;
    }

    /**
     * 处理没有括号的存储过程声明
     * CREATE PROCEDURE name param1 type1, param2 type2 AS BEGIN
     */
    private String convertProcedureWithoutParentheses(String code, ConversionResult result) {
        // 匹配没有括号的存储过程声明，参数直接跟在过程名后面
        Pattern pattern = Pattern.compile(
            "(CREATE|ALTER)\\s+PROCEDURE\\s+(?:\\[?\\w+\\]?\\.)?\\[?(\\w+)\\]?\\s*\\n([\\s\\S]*?)\\s*AS\\s*\\n?\\s*BEGIN",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String operation = matcher.group(1);
            String procName = matcher.group(2);
            String parametersBlock = matcher.group(3);

            // 解析参数块
            String convertedParams = parseParametersFromBlock(parametersBlock, result);

            StringBuilder replacement = new StringBuilder();
            replacement.append("CREATE OR REPLACE FUNCTION ").append(procName).append("(");
            replacement.append(convertedParams);
            replacement.append(") RETURNS VOID AS \\$BODY\\$\nBEGIN");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("存储过程已转换为函数: " + operation + " PROCEDURE " + procName + " -> CREATE OR REPLACE FUNCTION " + procName);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 从参数块中解析参数
     */
    private String parseParametersFromBlock(String parametersBlock, ConversionResult result) {
        if (parametersBlock == null || parametersBlock.trim().isEmpty()) {
            return "";
        }

        // 清理参数块，移除多余的空白
        String cleanedBlock = parametersBlock.trim();

        // 如果参数块不包含@符号，可能不是参数声明
        if (!cleanedBlock.contains("@")) {
            return "";
        }

        // 按行分割参数，但要考虑参数可能跨行
        String[] lines = cleanedBlock.split("\\n");
        StringBuilder sb = new StringBuilder();
        boolean hasValidParams = false;
        StringBuilder currentParam = new StringBuilder();

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            // 如果行以@开头，这是一个新参数的开始
            if (line.startsWith("@")) {
                // 处理之前累积的参数
                if (currentParam.length() > 0) {
                    String processedParam = processParameterLine(currentParam.toString(), result);
                    if (!processedParam.isEmpty()) {
                        if (hasValidParams) {
                            sb.append(", ");
                        }
                        sb.append(processedParam);
                        hasValidParams = true;
                    }
                    currentParam.setLength(0);
                }

                // 开始新参数
                currentParam.append(line);
            } else {
                // 这可能是参数的延续行，添加到当前参数
                if (currentParam.length() > 0) {
                    currentParam.append(" ").append(line);
                }
            }
        }

        // 处理最后一个参数
        if (currentParam.length() > 0) {
            String processedParam = processParameterLine(currentParam.toString(), result);
            if (!processedParam.isEmpty()) {
                if (hasValidParams) {
                    sb.append(", ");
                }
                sb.append(processedParam);
                hasValidParams = true;
            }
        }

        return sb.toString();
    }

    /**
     * 处理单个参数行
     */
    private String processParameterLine(String line, ConversionResult result) {
        if (line == null || line.trim().isEmpty()) {
            return "";
        }

        // 移除行末的逗号
        if (line.endsWith(",")) {
            line = line.substring(0, line.length() - 1).trim();
        }

        // 移除@符号
        if (line.startsWith("@")) {
            line = line.substring(1);
        }

        // 处理OUTPUT参数 - 记录但不完全移除，用于确定返回类型
        boolean isOutputParam = false;
        String outputParamType = null;
        if (line.toUpperCase().contains("OUTPUT")) {
            isOutputParam = true;
            // 提取参数类型用于返回类型
            String[] parts = line.split("\\s+");
            for (int i = 0; i < parts.length; i++) {
                if (parts[i].toUpperCase().equals("OUTPUT") && i > 0) {
                    outputParamType = parts[i-1];
                    break;
                }
            }

            line = line.replaceAll("(?i)\\s+OUTPUT\\s*", " ");
            line = line.replaceAll("(?i)OUTPUT\\s*", "");
            line = line.trim();

            // 将OUTPUT参数信息存储到result中，供后续处理使用
            if (outputParamType != null) {
                result.addWarning("OUTPUT参数已转换为返回值类型: " + line + " -> " + outputParamType);
                // 可以在这里设置一个标记，表示需要修改返回类型
                result.getStatistics().setHasOutputParameter(true);
                result.getStatistics().setOutputParameterType(outputParamType);
            } else {
                result.addWarning("OUTPUT参数已移除: " + line);
            }

            // OUTPUT参数不包含在普通参数列表中
            return "";
        }

        // 清理注释
        line = cleanParameterComments(line);

        // 转换数据类型
        line = convertParameterDataType(line);

        return line.trim();
    }

    /**
     * 转换参数中的数据类型
     */
    private String convertParameterDataType(String param) {
        if (param == null || param.trim().isEmpty()) {
            return param;
        }

        // 转换NTEXT为TEXT
        param = param.replaceAll("(?i)\\bNTEXT\\b", "TEXT");

        // 转换NVARCHAR为VARCHAR
        param = param.replaceAll("(?i)\\bNVARCHAR\\b", "VARCHAR");

        return param;
    }

    /**
     * 转换函数声明
     * ALTER FUNCTION [dbo].[funName] -> CREATE OR REPLACE FUNCTION funName
     */
    private String convertFunctionDeclaration(String code, ConversionResult result) {
        String convertedCode = code;

        // 首先处理标准的带括号的函数声明
        Pattern alterPattern = Pattern.compile(
            "(ALTER|CREATE)\\s+FUNCTION\\s+(?:\\[?\\w+\\]?\\.)?\\[?(\\w+)\\]?\\s*\\((.*?)\\)\\s*RETURNS\\s+(\\w+(?:\\s*\\([^)]*\\))?)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = alterPattern.matcher(convertedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String operation = matcher.group(1);
            String funcName = matcher.group(2);
            String parameters = matcher.group(3);
            String returnType = matcher.group(4);

            StringBuilder replacement = new StringBuilder();
            replacement.append("CREATE OR REPLACE FUNCTION ").append(funcName).append("(");

            if (parameters != null && !parameters.trim().isEmpty()) {
                // 转换参数列表
                String convertedParams = convertParameterList(parameters, result);
                replacement.append(convertedParams);
            }

            replacement.append(") RETURNS ").append(convertDataType(returnType)).append(" AS \\$BODY\\$");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("函数声明已转换: " + operation + " FUNCTION -> CREATE OR REPLACE FUNCTION");
        }
        matcher.appendTail(sb);
        convertedCode = sb.toString();

        // 处理没有括号的函数声明（参数直接跟在函数名后面）
        convertedCode = convertFunctionWithoutParentheses(convertedCode, result);

        return convertedCode;
    }

    /**
     * 处理没有括号的函数声明
     * CREATE FUNCTION name param1 type1, param2 type2 AS BEGIN
     */
    private String convertFunctionWithoutParentheses(String code, ConversionResult result) {
        // 匹配没有括号的函数声明，参数直接跟在函数名后面
        Pattern pattern = Pattern.compile(
            "(CREATE|ALTER)\\s+FUNCTION\\s+(?:\\[?\\w+\\]?\\.)?\\[?(\\w+)\\]?\\s*\\n([\\s\\S]*?)\\s*AS\\s*\\n?\\s*BEGIN",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String operation = matcher.group(1);
            String funcName = matcher.group(2);
            String parametersBlock = matcher.group(3);

            // 解析参数块
            String convertedParams = parseParametersFromBlock(parametersBlock, result);

            StringBuilder replacement = new StringBuilder();
            replacement.append("CREATE OR REPLACE FUNCTION ").append(funcName).append("(");
            replacement.append(convertedParams);
            replacement.append(") RETURNS VOID AS \\$BODY\\$\nBEGIN");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("函数声明已转换: " + operation + " FUNCTION " + funcName + " -> CREATE OR REPLACE FUNCTION " + funcName);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换数据类型
     */
    private String convertDataType(String sqlServerType) {
        if (sqlServerType == null) return "VOID";

        String upperType = sqlServerType.toUpperCase().trim();

        // 基本数据类型映射
        if (upperType.startsWith("VARCHAR")) return sqlServerType;
        if (upperType.startsWith("NVARCHAR")) return sqlServerType.replace("NVARCHAR", "VARCHAR");
        if (upperType.equals("INT")) return "INTEGER";
        if (upperType.equals("DATETIME")) return "TIMESTAMP";
        if (upperType.equals("BIT")) return "BOOLEAN";
        if (upperType.startsWith("DECIMAL")) return sqlServerType;
        if (upperType.startsWith("NUMERIC")) return sqlServerType;

        return sqlServerType; // 保持原样如果没有映射
    }

    /**
     * 转换参数列表
     */
    private String convertParameterList(String parameters, ConversionResult result) {
        if (parameters == null || parameters.trim().isEmpty()) {
            return "";
        }

        // 移除@符号并转换参数
        String[] params = parameters.split(",");
        StringBuilder sb = new StringBuilder();
        boolean hasValidParams = false;

        for (int i = 0; i < params.length; i++) {
            String param = params[i].trim();

            // 跳过空参数
            if (param.isEmpty()) {
                continue;
            }

            // 移除@符号
            if (param.startsWith("@")) {
                param = param.substring(1);
            }

            // 处理OUTPUT参数 - 更强的检测和处理
            if (param.toUpperCase().contains("OUTPUT")) {
                // 移除OUTPUT关键字及其周围的空白
                param = param.replaceAll("(?i)\\s+OUTPUT\\s*", " ");
                param = param.replaceAll("(?i)OUTPUT\\s*", "");
                param = param.trim();

                // 如果参数只包含OUTPUT关键字，跳过这个参数
                if (param.isEmpty() || param.toUpperCase().equals("OUTPUT")) {
                    result.addWarning("OUTPUT参数已完全移除: " + params[i].trim());
                    continue;
                }

                result.addWarning("OUTPUT参数已转换为普通参数，可能需要手动调整返回值: " + param);
            }

            // 清理参数中的注释
            param = cleanParameterComments(param);

            // 如果参数不为空，添加到结果中
            if (!param.trim().isEmpty()) {
                if (hasValidParams) {
                    sb.append(", ");
                }
                sb.append(param.trim());
                hasValidParams = true;
            }
        }

        return sb.toString();
    }

    /**
     * 清理参数中的注释
     */
    private String cleanParameterComments(String param) {
        // 移除行内注释 (-- 注释)
        int commentIndex = param.indexOf("--");
        if (commentIndex != -1) {
            param = param.substring(0, commentIndex).trim();
        }

        // 移除块注释 (/* 注释 */)
        param = param.replaceAll("/\\*.*?\\*/", "").trim();

        return param;
    }

    /**
     * 转换变量声明
     * 将所有DECLARE语句移到BEGIN后面，并正确格式化
     */
    private String convertVariableDeclarations(String code, ConversionResult result) {
        // 收集所有的DECLARE语句
        Pattern pattern = Pattern.compile(
            "\\s*DECLARE\\s+@?(\\w+)\\s+(\\w+(?:\\s*\\([^)]+\\))?)([^;\\n]*);?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuilder declarations = new StringBuilder();
        StringBuffer codeWithoutDeclares = new StringBuffer();
        boolean hasDeclarations = false;

        while (matcher.find()) {
            String varName = matcher.group(1);
            String dataType = matcher.group(2);
            String comment = matcher.group(3).trim();

            // 清理注释，移除分号和多余空格
            comment = comment.replaceAll(";\\s*$", "").trim();

            // 添加到声明块，注释放在单独行
            if (!comment.isEmpty() && comment.startsWith("--")) {
                declarations.append("    -- ").append(comment.substring(2).trim()).append("\n");
            } else if (!comment.isEmpty()) {
                declarations.append("    -- ").append(comment).append("\n");
            }

            declarations.append("    ").append(varName).append(" ").append(dataType).append(";\n");
            hasDeclarations = true;

            // 从原代码中移除这个DECLARE语句
            matcher.appendReplacement(codeWithoutDeclares, "");
        }
        matcher.appendTail(codeWithoutDeclares);

        // 如果有声明，重新组织代码结构
        if (hasDeclarations) {
            String cleanCode = codeWithoutDeclares.toString();

            // 查找BEGIN位置
            Pattern beginPattern = Pattern.compile("(BEGIN\\s*\\n)", Pattern.CASE_INSENSITIVE);
            Matcher beginMatcher = beginPattern.matcher(cleanCode);

            if (beginMatcher.find()) {
                // 在BEGIN前插入DECLARE块
                StringBuilder newCode = new StringBuilder();
                newCode.append(cleanCode.substring(0, beginMatcher.start()));
                newCode.append("DECLARE\n");
                newCode.append(declarations.toString());
                newCode.append("BEGIN\n");
                newCode.append(cleanCode.substring(beginMatcher.end()));

                result.addWarning("变量声明已移动到BEGIN前面并合并为DECLARE块");
                return newCode.toString();
            }
        }

        return code;
    }

    /**
     * 转换变量引用
     * 移除函数体中的@符号
     */
    private String convertVariableReferences(String code, ConversionResult result) {
        // 移除变量引用中的@符号，但保留参数声明中的@符号处理
        Pattern pattern = Pattern.compile(
            "(?<!DECLARE\\s)@(\\w+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String varName = matcher.group(1);
            matcher.appendReplacement(sb, varName);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换临时表语法
     * SQL Server: #tableName -> KingBase: temp_tableName 或使用临时表语法
     */
    private String convertTemporaryTables(String code, ConversionResult result) {
        // 转换 #tableName 为 temp_tableName
        Pattern tempTablePattern = Pattern.compile(
            "#(\\w+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = tempTablePattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String tableName = matcher.group(1);
            String replacement = "temp_" + tableName;
            matcher.appendReplacement(sb, replacement);
            result.addWarning("临时表已转换: #" + tableName + " -> " + replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换EXEC语句
     * 处理SQL Server特有的存储过程调用
     */
    private String convertExecStatements(String code, ConversionResult result) {
        String convertedCode = code;

        // 首先处理包含openxml的复杂XML语句块
        Pattern xmlWithOpenxmlPattern = Pattern.compile(
            "exec\\s+sp_xml_preparedocument\\s+(\\w+)\\s*,\\s*(\\w+)\\s*([\\s\\S]*?openxml[\\s\\S]*?)(?=\\n\\s*(?:exec|select|insert|update|delete|declare|end|$))",
            Pattern.CASE_INSENSITIVE
        );

        Matcher xmlWithOpenxmlMatcher = xmlWithOpenxmlPattern.matcher(convertedCode);
        StringBuffer xmlSb = new StringBuffer();

        while (xmlWithOpenxmlMatcher.find()) {
            String docHandle = xmlWithOpenxmlMatcher.group(1);
            String xmlData = xmlWithOpenxmlMatcher.group(2);
            String openxmlBlock = xmlWithOpenxmlMatcher.group(3);

            // 转换为KingBase兼容的XML处理
            String kingbaseXml = convertXmlBlockToKingBase(docHandle, xmlData, openxmlBlock, result);

            xmlWithOpenxmlMatcher.appendReplacement(xmlSb, Matcher.quoteReplacement(kingbaseXml));
            result.addWarning("XML处理已转换为KingBase兼容格式，请验证XML路径和数据类型");
        }
        xmlWithOpenxmlMatcher.appendTail(xmlSb);
        convertedCode = xmlSb.toString();

        // 转换简单的 sp_xml_preparedocument
        Pattern xmlSimplePattern = Pattern.compile(
            "exec\\s+sp_xml_preparedocument\\s+(\\w+)\\s*,\\s*(\\w+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher xmlSimpleMatcher = xmlSimplePattern.matcher(convertedCode);
        if (xmlSimpleMatcher.find()) {
            String docHandle = xmlSimpleMatcher.group(1);
            String xmlData = xmlSimpleMatcher.group(2);

            String replacement = "-- XML处理需要手动实现\n" +
                               "-- 原始: exec sp_xml_preparedocument " + docHandle + ", " + xmlData + "\n" +
                               "-- KingBase不直接支持sp_xml_preparedocument，需要使用XML函数替代";

            convertedCode = xmlSimpleMatcher.replaceAll(replacement);
            result.addWarning("sp_xml_preparedocument已注释，需要手动实现XML处理逻辑");
        }

        // 转换剩余的EXEC语句（包括sp_xml_preparedocument）
        Pattern execPattern = Pattern.compile(
            "exec\\s+([\\w_]+)([^\\n]*)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher execMatcher = execPattern.matcher(convertedCode);
        StringBuffer sb = new StringBuffer();

        while (execMatcher.find()) {
            String procName = execMatcher.group(1);
            String params = execMatcher.group(2);

            if (procName.toLowerCase().equals("sp_xml_preparedocument")) {
                // 特殊处理sp_xml_preparedocument
                String replacement = "-- XML处理需要手动实现\n" +
                                   "-- 原始: exec " + procName + params + "\n" +
                                   "-- KingBase不支持sp_xml_preparedocument，需要使用XML函数替代";
                execMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
                result.addWarning("sp_xml_preparedocument已注释，需要手动实现XML处理逻辑");
            } else if (procName.toLowerCase().startsWith("sp_")) {
                // 其他系统存储过程也注释掉
                String replacement = "-- 系统存储过程不支持\n" +
                                   "-- 原始: exec " + procName + params + "\n" +
                                   "-- 需要使用KingBase等效功能替代";
                execMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
                result.addWarning("系统存储过程已注释: " + procName);
            } else {
                // 用户定义的存储过程转换为函数调用
                String replacement = "PERFORM " + procName + "(" + params.trim() + ")";
                execMatcher.appendReplacement(sb, replacement);
                result.addWarning("EXEC语句已转换为PERFORM: " + procName);
            }
        }
        execMatcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换OPENXML语句
     * 将SQL Server的OPENXML转换为KingBase兼容的XML处理方式
     */
    private String convertOpenXmlStatements(String code, ConversionResult result) {
        // 转换 openxml 函数调用
        Pattern openxmlPattern = Pattern.compile(
            "SELECT\\s+([\\s\\S]*?)\\s+FROM\\s+openxml\\s*\\(([^)]+)\\)\\s*with\\s*\\(([^)]+)\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = openxmlPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String selectClause = matcher.group(1).trim();
            String openxmlParams = matcher.group(2).trim();
            String withClause = matcher.group(3).trim();

            // 解析openxml参数
            String[] params = openxmlParams.split(",");
            String docHandle = params.length > 0 ? params[0].trim() : "xml_doc";
            String xpath = params.length > 1 ? params[1].trim().replaceAll("'", "") : "/root";

            // 解析WITH子句中的列定义
            String kingbaseQuery = convertOpenXmlToKingBase(selectClause, docHandle, xpath, withClause, result);

            matcher.appendReplacement(sb, Matcher.quoteReplacement(kingbaseQuery));
            result.addWarning("OPENXML已转换为KingBase的XML函数，请验证XML路径和数据类型");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 将OPENXML转换为KingBase的XML查询
     */
    private String convertOpenXmlToKingBase(String selectClause, String docHandle, String xpath, String withClause, ConversionResult result) {
        StringBuilder sb = new StringBuilder();

        // 解析WITH子句中的列定义
        String[] columns = withClause.split(",");

        sb.append("-- 转换自OPENXML的KingBase XML查询\n");
        sb.append("SELECT\n");

        for (int i = 0; i < columns.length; i++) {
            String column = columns[i].trim();
            String[] parts = column.split("\\s+");
            if (parts.length >= 2) {
                String columnName = parts[0].trim();
                String dataType = parts[1].trim();

                // 构建xpath表达式
                String xpathExpr = xpath + "/" + columnName;

                sb.append("    (xpath('").append(xpathExpr).append("/text()', ").append(docHandle).append("::xml))[1]::text");

                // 添加数据类型转换
                if (dataType.toUpperCase().startsWith("INT")) {
                    sb.append("::INTEGER");
                } else if (dataType.toUpperCase().startsWith("VARCHAR")) {
                    sb.append("::VARCHAR");
                } else if (dataType.toUpperCase().startsWith("CHAR")) {
                    sb.append("::CHAR");
                }

                sb.append(" AS ").append(columnName);

                if (i < columns.length - 1) {
                    sb.append(",\n");
                }
            }
        }

        sb.append("\nFROM (SELECT 1) AS dummy_table");

        return sb.toString();
    }

    /**
     * 将整个XML处理块转换为KingBase兼容格式
     */
    private String convertXmlBlockToKingBase(String docHandle, String xmlData, String openxmlBlock, ConversionResult result) {
        StringBuilder sb = new StringBuilder();

        sb.append("-- 转换自SQL Server XML处理的KingBase代码\n");
        sb.append("-- 原始使用sp_xml_preparedocument和openxml\n\n");

        // 查找CREATE TEMP TABLE语句，使用更强的正则表达式来匹配WITH子句
        Pattern createTablePattern = Pattern.compile(
            "CREATE\\s+TEMP\\s+TABLE\\s+(\\w+)\\s+AS\\s*\\n?\\s*SELECT\\s+([\\s\\S]*?)\\s+FROM\\s+openxml\\s*\\(([^)]+)\\)\\s*with\\s*\\(([\\s\\S]*?)\\)(?=\\s*;|\\s*$)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = createTablePattern.matcher(openxmlBlock);

        if (matcher.find()) {
            String tableName = matcher.group(1);
            String selectClause = matcher.group(2).trim();
            String openxmlParams = matcher.group(3).trim();
            String withClause = matcher.group(4).trim();

            // 解析openxml参数
            String[] params = openxmlParams.split(",");
            String xpath = params.length > 1 ? params[1].trim().replaceAll("'", "") : "/root";

            // 生成KingBase兼容的CREATE TABLE AS语句
            sb.append("CREATE TEMP TABLE ").append(tableName).append(" AS\n");
            sb.append("SELECT\n");

            // 解析WITH子句中的列定义
            String cleanedWithClause = withClause.replaceAll("\\s+", " ").trim();
            String[] columns = parseColumnDefinitions(cleanedWithClause);

            System.out.println("开始生成列定义，总共 " + columns.length + " 列");

            boolean hasValidColumns = false;
            for (int i = 0; i < columns.length; i++) {
                String column = columns[i].trim();
                String[] parts = column.split("\\s+", 2);
                if (parts.length >= 2) {
                    String columnName = parts[0].trim();
                    String dataType = parts[1].trim();

                    // 构建xpath表达式
                    String xpathExpr = xpath + "/" + columnName;

                    if (hasValidColumns) {
                        sb.append(",\n");
                    }

                    sb.append("    (xpath('").append(xpathExpr).append("/text()', ").append(xmlData).append("::xml))[1]::text");

                    // 添加数据类型转换
                    if (dataType.toUpperCase().startsWith("INT")) {
                        sb.append("::INTEGER");
                    } else if (dataType.toUpperCase().contains("VARCHAR")) {
                        sb.append("::VARCHAR");
                    } else if (dataType.toUpperCase().startsWith("CHAR")) {
                        sb.append("::CHAR");
                    } else if (dataType.toUpperCase().contains("NUMERIC")) {
                        sb.append("::NUMERIC");
                    }

                    sb.append(" AS ").append(columnName);
                    hasValidColumns = true;

                    System.out.println("处理列: " + columnName + " (" + dataType + ")");
                }
            }

            sb.append(";\n");

        } else {
            // 如果无法解析，提供通用的转换模板
            sb.append("-- 无法自动转换复杂的XML处理，请手动实现\n");
            sb.append("-- 使用KingBase的XML函数，例如:\n");
            sb.append("-- SELECT xpath('//element/text()', ").append(xmlData).append("::xml) AS result;\n");
            sb.append("-- 或使用unnest()函数处理数组结果:\n");
            sb.append("-- SELECT unnest(xpath('//element/text()', ").append(xmlData).append("::xml))::text AS element_value;\n");
        }

        return sb.toString();
    }

    /**
     * 解析WITH子句中的列定义
     */
    private String[] parseColumnDefinitions(String withClause) {
        if (withClause == null || withClause.trim().isEmpty()) {
            return new String[0];
        }

        // 清理输入，移除多余的空白和换行
        String cleaned = withClause.replaceAll("\\s+", " ").trim();

        // 处理复杂的列定义，考虑括号内的参数
        java.util.List<String> columns = new java.util.ArrayList<>();
        StringBuilder currentColumn = new StringBuilder();
        int parenthesesLevel = 0;

        for (int i = 0; i < cleaned.length(); i++) {
            char c = cleaned.charAt(i);

            if (c == '(') {
                parenthesesLevel++;
                currentColumn.append(c);
            } else if (c == ')') {
                parenthesesLevel--;
                currentColumn.append(c);
            } else if (c == ',' && parenthesesLevel == 0) {
                // 只有在括号外的逗号才是列分隔符
                if (currentColumn.length() > 0) {
                    String column = currentColumn.toString().trim();
                    if (!column.isEmpty()) {
                        columns.add(column);
                    }
                    currentColumn.setLength(0);
                }
            } else {
                currentColumn.append(c);
            }
        }

        // 添加最后一列
        if (currentColumn.length() > 0) {
            String column = currentColumn.toString().trim();
            if (!column.isEmpty()) {
                columns.add(column);
            }
        }

        // 调试输出
        System.out.println("解析的列定义数量: " + columns.size());
        for (int i = 0; i < columns.size(); i++) {
            System.out.println("列 " + i + ": " + columns.get(i));
        }

        return columns.toArray(new String[0]);
    }

    /**
     * 转换SELECT INTO语句
     * SQL Server: SELECT * INTO #table FROM source -> KingBase: CREATE TEMP TABLE
     */
    private String convertSelectIntoStatements(String code, ConversionResult result) {
        String convertedCode = code;

        // 首先处理包含OPENXML的SELECT INTO语句
        convertedCode = convertSelectIntoWithOpenXml(convertedCode, result);

        // 然后处理普通的SELECT INTO语句
        convertedCode = convertSimpleSelectInto(convertedCode, result);

        return convertedCode;
    }

    /**
     * 转换包含OPENXML的SELECT INTO语句
     */
    private String convertSelectIntoWithOpenXml(String code, ConversionResult result) {
        // 匹配: SELECT * INTO #table FROM openxml(...) WITH (...)
        Pattern selectIntoOpenXmlPattern = Pattern.compile(
            "SELECT\\s+(\\*|[^\\n]*?)\\s+INTO\\s+(#\\w+|temp_\\w+)\\s+FROM\\s+openxml\\s*\\(([^)]+)\\)\\s*WITH\\s*\\(([\\s\\S]*?)\\)(?=\\s*;|\\s*$|\\s*\\n\\s*(?:exec|select|insert|update|delete|declare|end))",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = selectIntoOpenXmlPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String selectClause = matcher.group(1).trim();
            String tableName = matcher.group(2).trim();
            String openxmlParams = matcher.group(3).trim();
            String withClause = matcher.group(4).trim();

            // 如果是#tableName，转换为temp_tableName
            if (tableName.startsWith("#")) {
                tableName = "temp_" + tableName.substring(1);
            }

            // 解析openxml参数
            String[] params = openxmlParams.split(",");
            String xmlDoc = params.length > 0 ? params[0].trim() : "xml_doc";
            String xpath = params.length > 1 ? params[1].trim().replaceAll("'", "") : "/root";

            // 构建KingBase兼容的CREATE TEMP TABLE语句
            StringBuilder replacement = new StringBuilder();
            replacement.append("CREATE TEMP TABLE ").append(tableName).append(" AS\n");
            replacement.append("SELECT\n");

            // 解析WITH子句中的列定义
            String[] columns = parseWithClauseForSelectInto(withClause);

            for (int i = 0; i < columns.length; i++) {
                String column = columns[i].trim();
                String[] parts = column.split("\\s+", 2);
                if (parts.length >= 2) {
                    String columnName = parts[0].trim();
                    String dataType = parts[1].trim();

                    // 移除方括号
                    columnName = columnName.replaceAll("\\[|\\]", "");

                    // 构建xpath表达式
                    String xpathExpr = xpath + "/" + columnName;

                    replacement.append("    (xpath('").append(xpathExpr).append("/text()', ").append(xmlDoc).append("::xml))[1]::text");

                    // 添加数据类型转换
                    if (dataType.toUpperCase().startsWith("INT")) {
                        replacement.append("::INTEGER");
                    } else if (dataType.toUpperCase().contains("VARCHAR")) {
                        replacement.append("::VARCHAR");
                    } else if (dataType.toUpperCase().startsWith("CHAR")) {
                        replacement.append("::CHAR");
                    } else if (dataType.toUpperCase().contains("NUMERIC")) {
                        replacement.append("::NUMERIC");
                    }

                    replacement.append(" AS ").append(columnName);

                    if (i < columns.length - 1) {
                        replacement.append(",\n");
                    }
                }
            }

            replacement.append("\nFROM (SELECT 1) AS dummy_table;");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("SELECT INTO + OPENXML语句已转换为CREATE TEMP TABLE AS + xpath: " + tableName);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 解析WITH子句用于SELECT INTO转换
     */
    private String[] parseWithClauseForSelectInto(String withClause) {
        if (withClause == null || withClause.trim().isEmpty()) {
            return new String[0];
        }

        // 移除多余的空白和换行，处理注释
        String cleaned = withClause.replaceAll("\\s+", " ").trim();

        // 移除注释行
        cleaned = cleaned.replaceAll("--[^\\n]*", "");

        // 简单的逗号分割
        String[] parts = cleaned.split(",");

        // 清理每个部分
        for (int i = 0; i < parts.length; i++) {
            parts[i] = parts[i].trim();
        }

        return parts;
    }

    /**
     * 转换简单的SELECT INTO语句
     */
    private String convertSimpleSelectInto(String code, ConversionResult result) {
        // 转换 SELECT ... INTO #tableName 语法（不包含OPENXML）
        Pattern selectIntoPattern = Pattern.compile(
            "SELECT\\s+(.*?)\\s+INTO\\s+(temp_\\w+|#\\w+)\\s+(FROM\\s+(?!openxml)[\\s\\S]*?)(?=\\n\\s*(?:exec|select|insert|update|delete|declare|end|$)|;)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = selectIntoPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String selectClause = matcher.group(1).trim();
            String tableName = matcher.group(2).trim();
            String fromClause = matcher.group(3).trim();

            // 如果是#tableName，转换为temp_tableName
            if (tableName.startsWith("#")) {
                tableName = "temp_" + tableName.substring(1);
            }

            // 构建KingBase兼容的语法
            StringBuilder replacement = new StringBuilder();
            replacement.append("CREATE TEMP TABLE ").append(tableName).append(" AS\n");
            replacement.append("SELECT ").append(selectClause).append(" ").append(fromClause);

            matcher.appendReplacement(sb, replacement.toString());
            result.addWarning("SELECT INTO语句已转换为CREATE TEMP TABLE AS: " + tableName);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换DELETE语句
     * SQL Server: DELETE tablename -> KingBase: DELETE FROM tablename
     */
    private String convertDeleteStatements(String code, ConversionResult result) {
        // 转换 DELETE tablename 为 DELETE FROM tablename
        Pattern deletePattern = Pattern.compile(
            "DELETE\\s+(\\w+)(?!\\s+FROM)(?=\\s|$|;|\\n)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = deletePattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String tableName = matcher.group(1);
            String replacement = "DELETE FROM " + tableName;
            matcher.appendReplacement(sb, replacement);
            result.addWarning("DELETE语句已转换: DELETE " + tableName + " -> DELETE FROM " + tableName);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换INSERT语句
     * 确保INSERT语句语法正确
     */
    private String convertInsertStatements(String code, ConversionResult result) {
        // 修复INSERT INTO ... VALUES语法，确保有正确的空格
        Pattern insertPattern = Pattern.compile(
            "INSERT\\s+INTO\\s+(\\w+)\\s*\\(([^)]+)\\)\\s*VALUES\\s*\\(([^)]+)\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = insertPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String tableName = matcher.group(1);
            String columns = matcher.group(2).trim();
            String values = matcher.group(3).trim();

            String replacement = "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + values + ")";
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        // 处理没有空格的values语法
        String result1 = sb.toString();
        result1 = result1.replaceAll("(?i)\\)values\\(", ") VALUES (");

        return result1;
    }

    /**
     * 添加语句分隔符
     * 确保SQL语句之间有正确的分号分隔
     */
    private String addStatementSeparators(String code, ConversionResult result) {
        String[] lines = code.split("\\n");
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // 跳过空行和注释行
            if (line.isEmpty() || line.startsWith("--") || line.startsWith("/*")) {
                sb.append(lines[i]).append("\n");
                continue;
            }

            // 需要分号的语句类型（排除单独的DECLARE关键字）
            boolean needsSemicolon = line.toUpperCase().matches(".*\\b(DELETE|INSERT|UPDATE|SELECT|SET|CALL|PERFORM)\\b.*")
                                   && !line.endsWith(";")
                                   && !line.toUpperCase().contains("BEGIN")
                                   && !line.toUpperCase().contains("END")
                                   && !line.toUpperCase().contains("IF")
                                   && !line.toUpperCase().contains("WHILE")
                                   && !line.toUpperCase().contains("FOR")
                                   && !line.toUpperCase().trim().equals("DECLARE"); // 排除单独的DECLARE行

            if (needsSemicolon) {
                sb.append(lines[i]).append(";\n");
            } else {
                sb.append(lines[i]).append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 转换IF语句
     * 确保IF语句有正确的THEN关键字和END IF结构
     */
    private String convertIfStatements(String code, ConversionResult result) {
        String convertedCode = code;

        // 第一步：确保IF语句有THEN关键字
        convertedCode = addThenToIfStatements(convertedCode, result);

        // 第二步：修复IF...BEGIN...END结构为IF...THEN...END IF
        convertedCode = convertIfBeginEndToIfThenEndIf(convertedCode, result);

        // 第三步：确保所有IF语句以END IF结尾
        convertedCode = ensureIfStatementsEndWithEndIf(convertedCode, result);

        return convertedCode;
    }

    /**
     * 为IF语句添加THEN关键字
     */
    private String addThenToIfStatements(String code, ConversionResult result) {
        // 匹配IF语句，确保添加THEN关键字
        Pattern pattern = Pattern.compile(
            "IF\\s+([^\\n]+?)\\s*\\n",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String condition = matcher.group(1).trim();

            // 如果条件后面没有THEN，则添加
            if (!condition.toUpperCase().endsWith("THEN")) {
                condition += " THEN";
            }

            String replacement = "IF " + condition + "\n";
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 将IF...BEGIN...END结构转换为IF...THEN...END IF
     */
    private String convertIfBeginEndToIfThenEndIf(String code, ConversionResult result) {
        // 匹配IF...THEN BEGIN...END模式
        Pattern ifBeginEndPattern = Pattern.compile(
            "(IF\\s+[^\\n]*?\\s+THEN)\\s*\\n\\s*BEGIN\\s*\\n([\\s\\S]*?)\\n\\s*END\\s*;",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = ifBeginEndPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String ifCondition = matcher.group(1);
            String ifBody = matcher.group(2);

            // 清理IF体中的内容
            String cleanedBody = cleanAndFormatIfBody(ifBody);

            String replacement = ifCondition + "\n" + cleanedBody + "\n    END IF;";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            result.addWarning("修复了IF...THEN BEGIN...END结构为IF...THEN...END IF");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 确保所有IF语句以END IF结尾
     */
    private String ensureIfStatementsEndWithEndIf(String code, ConversionResult result) {
        // 查找IF...THEN...END;模式并修复为IF...THEN...END IF;
        Pattern ifEndPattern = Pattern.compile(
            "(IF\\s+[^\\n]*?THEN[\\s\\S]*?)\\bEND\\s*;(?!\\s*IF)(?!\\s*\\$BODY\\$)(?!\\s*LOOP)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = ifEndPattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String ifBlock = matcher.group(1);
            String replacement = ifBlock + "END IF;";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            result.addWarning("修复了IF语句结束语法：END; -> END IF;");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换WHILE循环
     * 确保WHILE循环有正确的LOOP关键字
     */
    private String convertWhileLoops(String code, ConversionResult result) {
        // 转换WHILE循环结构
        Pattern pattern = Pattern.compile(
            "WHILE\\s+([^\\n]+?)\\s*\\n\\s*BEGIN",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String condition = matcher.group(1).trim();
            
            String replacement = "WHILE " + condition + " LOOP";
            matcher.appendReplacement(sb, replacement);
            result.addWarning("WHILE循环已转换，请检查END语句是否需要改为END LOOP");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换TRY-CATCH语句
     */
    private String convertTryCatchStatements(String code, ConversionResult result) {
        // SQL Server的TRY-CATCH需要转换为PostgreSQL的异常处理
        Pattern tryPattern = Pattern.compile(
            "BEGIN\\s+TRY",
            Pattern.CASE_INSENSITIVE
        );

        Pattern catchPattern = Pattern.compile(
            "END\\s+TRY\\s*BEGIN\\s+CATCH",
            Pattern.CASE_INSENSITIVE
        );

        Pattern endCatchPattern = Pattern.compile(
            "END\\s+CATCH",
            Pattern.CASE_INSENSITIVE
        );

        String convertedCode = code;
        
        convertedCode = tryPattern.matcher(convertedCode).replaceAll("BEGIN");
        convertedCode = catchPattern.matcher(convertedCode).replaceAll("EXCEPTION WHEN OTHERS THEN");
        convertedCode = endCatchPattern.matcher(convertedCode).replaceAll("END");

        if (!convertedCode.equals(code)) {
            result.addWarning("TRY-CATCH语句已转换为EXCEPTION处理，请检查异常处理逻辑");
        }

        return convertedCode;
    }

    /**
     * 转换OUTPUT参数
     */
    private String convertOutputParameters(String code, ConversionResult result) {
        String convertedCode = code;

        // 模式1: @参数名 数据类型 OUTPUT
        Pattern pattern1 = Pattern.compile(
            "@(\\w+)\\s+(\\w+(?:\\s*\\([^)]*\\))?)\\s+OUTPUT",
            Pattern.CASE_INSENSITIVE
        );

        // 模式2: 参数名 数据类型 OUTPUT (已经移除@符号的情况)
        Pattern pattern2 = Pattern.compile(
            "\\b(\\w+)\\s+(\\w+(?:\\s*\\([^)]*\\))?)\\s+OUTPUT\\b",
            Pattern.CASE_INSENSITIVE
        );

        // 模式3: OUTPUT关键字单独出现
        Pattern pattern3 = Pattern.compile(
            "\\s+OUTPUT\\s*,?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher1 = pattern1.matcher(convertedCode);
        if (matcher1.find()) {
            convertedCode = matcher1.replaceAll("$1 $2");
            result.addWarning("检测到OUTPUT参数，已转换为普通参数，建议修改为使用返回值");
        }

        Matcher matcher2 = pattern2.matcher(convertedCode);
        if (matcher2.find()) {
            convertedCode = matcher2.replaceAll("$1 $2");
            result.addWarning("检测到OUTPUT参数，已转换为普通参数，建议修改为使用返回值");
        }

        Matcher matcher3 = pattern3.matcher(convertedCode);
        if (matcher3.find()) {
            convertedCode = matcher3.replaceAll(" ");
            result.addWarning("检测到独立的OUTPUT关键字，已移除");
        }

        return convertedCode;
    }

    /**
     * 转换SET语句
     * SET @var = value -> var := value;
     */
    private String convertSetStatements(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "SET\\s+@(\\w+)\\s*=\\s*([^;\\n]+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String varName = matcher.group(1);
            String value = matcher.group(2).trim();

            String replacement = varName + " := " + value + ";";
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换PRINT语句
     * PRINT -> RAISE NOTICE
     */
    private String convertPrintStatements(String code, ConversionResult result) {
        Pattern pattern = Pattern.compile(
            "PRINT\\s+([^;\\n]+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(code);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String message = matcher.group(1).trim();
            
            String replacement = "RAISE NOTICE " + message + ";";
            matcher.appendReplacement(sb, replacement);
            result.addWarning("PRINT语句已转换为RAISE NOTICE");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 转换事务语句
     * 处理BEGIN TRAN、COMMIT TRAN、ROLLBACK TRAN等
     */
    private String convertTransactionStatements(String code, ConversionResult result) {
        String convertedCode = code;

        // 转换BEGIN TRAN/BEGIN TRANSACTION
        Pattern beginTranPattern = Pattern.compile(
            "BEGIN\\s+TRAN(?:SACTION)?(?:\\s+\\w+)?\\s*;?",
            Pattern.CASE_INSENSITIVE
        );
        if (beginTranPattern.matcher(convertedCode).find()) {
            convertedCode = beginTranPattern.matcher(convertedCode).replaceAll("BEGIN;");
            result.addWarning("BEGIN TRAN已转换为BEGIN");
        }

        // 转换COMMIT TRAN/COMMIT TRANSACTION
        Pattern commitTranPattern = Pattern.compile(
            "COMMIT\\s+TRAN(?:SACTION)?(?:\\s+\\w+)?\\s*;?",
            Pattern.CASE_INSENSITIVE
        );
        if (commitTranPattern.matcher(convertedCode).find()) {
            convertedCode = commitTranPattern.matcher(convertedCode).replaceAll("COMMIT;");
            result.addWarning("COMMIT TRAN已转换为COMMIT");
        }

        // 转换ROLLBACK TRAN/ROLLBACK TRANSACTION
        Pattern rollbackTranPattern = Pattern.compile(
            "ROLLBACK\\s+TRAN(?:SACTION)?(?:\\s+\\w+)?\\s*;?",
            Pattern.CASE_INSENSITIVE
        );
        if (rollbackTranPattern.matcher(convertedCode).find()) {
            convertedCode = rollbackTranPattern.matcher(convertedCode).replaceAll("ROLLBACK;");
            result.addWarning("ROLLBACK TRAN已转换为ROLLBACK");
        }

        return convertedCode;
    }

    /**
     * 转换注释
     * 确保注释格式兼容并修复未闭合的注释
     */
    private String convertComments(String code, ConversionResult result) {
        String convertedCode = code;

        // 修复未闭合的多行注释
        convertedCode = fixUnclosedComments(convertedCode, result);

        // 确保单行注释格式正确
        convertedCode = convertedCode.replaceAll("--\\s*", "-- ");

        // 确保多行注释格式正确
        convertedCode = convertedCode.replaceAll("/\\*\\s*", "/* ");
        convertedCode = convertedCode.replaceAll("\\s*\\*/", " */");

        // 处理完整的注释块（包含存储过程定义）
        convertedCode = handleCompleteCommentBlocks(convertedCode, result);

        return convertedCode;
    }

    /**
     * 修复未闭合的多行注释
     */
    private String fixUnclosedComments(String code, ConversionResult result) {
        String convertedCode = code;
        boolean hasFixedComment = false;

        // 使用更强大的方法来检测和修复未闭合的注释
        StringBuilder sb = new StringBuilder();
        boolean inComment = false;
        int commentStart = -1;

        for (int i = 0; i < convertedCode.length() - 1; i++) {
            char current = convertedCode.charAt(i);
            char next = convertedCode.charAt(i + 1);

            if (!inComment && current == '/' && next == '*') {
                // 开始注释
                inComment = true;
                commentStart = i;
                sb.append(current).append(next);
                i++; // 跳过下一个字符
            } else if (inComment && current == '*' && next == '/') {
                // 结束注释
                inComment = false;
                sb.append(current).append(next);
                i++; // 跳过下一个字符
            } else {
                sb.append(current);
            }
        }

        // 处理最后一个字符
        if (convertedCode.length() > 0) {
            sb.append(convertedCode.charAt(convertedCode.length() - 1));
        }

        // 如果还在注释中，说明注释没有闭合
        if (inComment) {
            sb.append(" */");
            hasFixedComment = true;
            result.addWarning("修复了未闭合的多行注释");
        }

        return hasFixedComment ? sb.toString() : convertedCode;
    }

    /**
     * 处理完整的注释块，包含存储过程定义的情况
     */
    private String handleCompleteCommentBlocks(String code, ConversionResult result) {
        String convertedCode = code;

        // 查找完整的多行注释块 /* ... */
        Pattern completeCommentPattern = Pattern.compile(
            "/\\*([\\s\\S]*?)\\*/",
            Pattern.DOTALL
        );

        Matcher matcher = completeCommentPattern.matcher(convertedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String commentContent = matcher.group(1);

            // 检查注释内容是否包含存储过程定义
            if (isCommentContainingProcedure(commentContent)) {
                // 如果包含存储过程定义，提取并转换存储过程
                String extractedProcedure = extractProcedureFromComment(commentContent, result);
                matcher.appendReplacement(sb, Matcher.quoteReplacement(extractedProcedure));
                result.addWarning("从注释块中提取了存储过程定义");
            } else {
                // 普通注释，转换为单行注释格式
                String convertedComment = convertToSingleLineComments(commentContent);
                matcher.appendReplacement(sb, Matcher.quoteReplacement(convertedComment));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 检查注释内容是否包含存储过程定义
     */
    private boolean isCommentContainingProcedure(String commentContent) {
        String upperContent = commentContent.toUpperCase();
        return upperContent.contains("ALTER PROCEDURE") ||
               upperContent.contains("CREATE PROCEDURE") ||
               upperContent.contains("CREATE OR REPLACE FUNCTION") ||
               (upperContent.contains("BEGIN") && upperContent.contains("END"));
    }

    /**
     * 从注释中提取存储过程定义
     */
    private String extractProcedureFromComment(String commentContent, ConversionResult result) {
        StringBuilder sb = new StringBuilder();
        String[] lines = commentContent.split("\\n");
        boolean inProcedureDefinition = false;

        for (String line : lines) {
            String trimmedLine = line.trim();

            // 检查是否是存储过程定义的开始
            if (trimmedLine.toUpperCase().contains("ALTER PROCEDURE") ||
                trimmedLine.toUpperCase().contains("CREATE PROCEDURE")) {
                inProcedureDefinition = true;
                sb.append(trimmedLine).append("\n");
            } else if (inProcedureDefinition) {
                // 在存储过程定义中
                sb.append(trimmedLine).append("\n");
            } else {
                // 不在存储过程定义中，作为注释处理
                if (!trimmedLine.isEmpty()) {
                    sb.append("-- ").append(trimmedLine).append("\n");
                }
            }
        }

        return sb.toString();
    }

    /**
     * 将多行注释内容转换为单行注释
     */
    private String convertToSingleLineComments(String commentContent) {
        StringBuilder sb = new StringBuilder();
        String[] lines = commentContent.split("\\n");

        for (String line : lines) {
            String trimmedLine = line.trim();
            if (!trimmedLine.isEmpty()) {
                sb.append("-- ").append(trimmedLine).append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 清理方括号语法
     * 移除SQL Server特有的方括号标识符语法
     */
    private String cleanBracketSyntax(String code, ConversionResult result) {
        String convertedCode = code;

        // 移除对象名称周围的方括号 [objectName] -> objectName
        // 但保留数组索引如 [1], [2] 等
        Pattern bracketPattern = Pattern.compile("\\[([a-zA-Z]\\w*)\\]");
        Matcher matcher = bracketPattern.matcher(convertedCode);

        if (matcher.find()) {
            convertedCode = matcher.replaceAll("$1");
            result.addWarning("已移除方括号语法，KingBase使用标准SQL标识符");
        }

        // 移除schema前缀 dbo.objectName -> objectName
        Pattern schemaPattern = Pattern.compile("\\bdbo\\.");
        Matcher schemaMatcher = schemaPattern.matcher(convertedCode);

        if (schemaMatcher.find()) {
            convertedCode = schemaMatcher.replaceAll("");
            result.addWarning("已移除dbo schema前缀");
        }

        return convertedCode;
    }

    /**
     * 修复常见的转换错误
     */
    private String fixCommonConversionErrors(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复函数参数问题
        fixedCode = fixFunctionParameters(fixedCode, result);

        // 修复SELECT语句语法错误
        fixedCode = fixSelectStatementErrors(fixedCode, result);

        // 强力修复孤立的xpath表达式（最后的保险措施）
        fixedCode = forceFixOrphanXpath(fixedCode, result);

        // 最终修复：处理您遇到的具体问题
        fixedCode = fixYourSpecificIssue(fixedCode, result);

        // 修复变量赋值语法
        fixedCode = fixVariableAssignmentSyntax(fixedCode, result);

        // 修复IF语句语法
        fixedCode = fixIfStatementSyntax(fixedCode, result);

        // 修复事务语法
        fixedCode = fixTransactionSyntax(fixedCode, result);

        // 修复UPDATE语句语法
        fixedCode = fixUpdateStatementSyntax(fixedCode, result);

        // 修复数据类型问题
        fixedCode = fixDataTypeIssues(fixedCode, result);

        // 修复表提示语法
        fixedCode = fixTableHints(fixedCode, result);

        return fixedCode;
    }

    /**
     * 修复函数参数问题
     */
    private String fixFunctionParameters(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找函数定义并添加缺失的参数
        Pattern functionPattern = Pattern.compile(
            "(CREATE OR REPLACE FUNCTION\\s+\\w+)\\(\\)\\s+(RETURNS\\s+VOID)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = functionPattern.matcher(fixedCode);
        if (matcher.find()) {
            // 检查是否有参数使用但未定义
            if (fixedCode.contains("chrXML") || fixedCode.contains("@chrXML")) {
                // 确定返回类型
                String returnType = "VARCHAR(50)"; // 默认返回类型

                // 如果有OUTPUT参数信息，使用OUTPUT参数的类型作为返回类型
                if (result.getStatistics().isHasOutputParameter()) {
                    String outputType = result.getStatistics().getOutputParameterType();
                    if (outputType != null && !outputType.isEmpty()) {
                        returnType = convertParameterDataType(outputType);
                    }
                }

                String replacement = matcher.group(1) + "(chrXML TEXT) RETURNS " + returnType;
                fixedCode = matcher.replaceFirst(replacement);

                // 修复chrReturn的使用
                fixedCode = fixChrReturnUsage(fixedCode);

                result.addWarning("添加了缺失的函数参数：chrXML，OUTPUT参数转换为返回值类型：" + returnType);
            }
        }

        return fixedCode;
    }

    /**
     * 修复chrReturn的使用方式
     */
    private String fixChrReturnUsage(String code) {
        String fixedCode = code;

        // 完全移除DECLARE块中的chrReturn相关声明
        fixedCode = fixedCode.replaceAll("(?i)\\s*(?:chrReturn|result_value)\\s+(?:OUT\\s+)?VARCHAR\\([^)]*\\);?\\s*\\n?", "");

        // 方案：不声明返回变量，直接在需要的地方使用RETURN语句
        // 将chrReturn赋值转换为直接返回或使用临时变量

        // 处理chrReturn := 'value' 的情况
        // 如果是在函数最后的赋值，直接转换为RETURN
        Pattern finalAssignPattern = Pattern.compile(
            "(?i)chrReturn\\s*:=\\s*([^;]+);\\s*(?=\\s*(?:END|EXCEPTION|RETURN))",
            Pattern.CASE_INSENSITIVE
        );

        Matcher finalMatcher = finalAssignPattern.matcher(fixedCode);
        if (finalMatcher.find()) {
            fixedCode = finalMatcher.replaceAll("RETURN $1;");
        }

        // 处理其他chrReturn赋值，使用临时变量
        fixedCode = fixedCode.replaceAll("(?i)\\bchrReturn\\s*:=", "-- chrReturn := ");

        // 如果函数末尾没有RETURN语句，添加默认返回
        if (!fixedCode.toUpperCase().contains("RETURN ")) {
            Pattern endPattern = Pattern.compile("(\\s*)(END;)\\s*(\\$BODY\\$)", Pattern.CASE_INSENSITIVE);
            Matcher endMatcher = endPattern.matcher(fixedCode);

            if (endMatcher.find()) {
                fixedCode = endMatcher.replaceFirst("$1    RETURN '0'; -- 默认返回值\n$1$2\n$3");
            }
        }

        return fixedCode;
    }

    /**
     * 修复SELECT语句语法错误
     */
    private String fixSelectStatementErrors(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复OPENXML转换产生的问题
        fixedCode = fixOpenXmlConversionIssues(fixedCode, result);

        // 修复孤立的xpath表达式
        fixedCode = fixOrphanXpathExpressions(fixedCode, result);

        // 修复特定的转换错误模式
        fixedCode = fixSpecificConversionPattern(fixedCode, result);

        // 修复最常见的xpath问题（简化版本）
        fixedCode = fixCommonXpathIssues(fixedCode, result);

        // 修复不完整的CREATE TEMP TABLE语句
        fixedCode = fixIncompleteCreateTempTable(fixedCode, result);

        return fixedCode;
    }

    /**
     * 修复孤立的xpath表达式（最常见的问题）
     */
    private String fixOrphanXpathExpressions(String code, ConversionResult result) {
        String fixedCode = code;

        // 第一步：修复最常见的模式 - 直接出现的孤立xpath表达式
        Pattern directXpathPattern = Pattern.compile(
            "\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?\\s*\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?)?[^\\n]*\\n\\s*FROM\\s+\\([^)]+\\)\\s+AS\\s+dummy_table;?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher directMatcher = directXpathPattern.matcher(fixedCode);
        StringBuffer sb1 = new StringBuffer();

        while (directMatcher.find()) {
            String xpath1 = directMatcher.group(1);
            String xpath2 = directMatcher.group(2);

            // 构建完整的CREATE TEMP TABLE语句
            StringBuilder replacement = new StringBuilder();
            replacement.append("\n        -- 修复的OPENXML转换\n");
            replacement.append("        CREATE TEMP TABLE temp_DataM AS\n");
            replacement.append("        SELECT\n");
            replacement.append("            ").append(xpath1.replaceAll("intDoc::xml", "chrXML::xml"));
            if (xpath2 != null && !xpath2.trim().isEmpty()) {
                replacement.append(",\n            ").append(xpath2.replaceAll("intDoc::xml", "chrXML::xml"));
            }
            // 添加其他字段
            replacement.append(",\n            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo");
            replacement.append(",\n            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason");
            replacement.append(",\n            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n");

            directMatcher.appendReplacement(sb1, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("修复了孤立的xpath表达式，构建了完整的CREATE TEMP TABLE语句");
        }
        directMatcher.appendTail(sb1);
        fixedCode = sb1.toString();

        // 第二步：处理任何剩余的单独xpath表达式
        Pattern remainingXpathPattern = Pattern.compile(
            "\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?;?\\s*\\n",
            Pattern.CASE_INSENSITIVE
        );

        Matcher remainingMatcher = remainingXpathPattern.matcher(fixedCode);
        StringBuffer sb2 = new StringBuffer();

        while (remainingMatcher.find()) {
            String xpathExpr = remainingMatcher.group(1);
            // 将剩余的孤立xpath表达式注释掉
            String replacement = "\n        -- 孤立的xpath表达式已注释：\n        -- " + xpathExpr + "\n";
            remainingMatcher.appendReplacement(sb2, replacement);
            result.addWarning("注释了剩余的孤立xpath表达式");
        }
        remainingMatcher.appendTail(sb2);

        return sb2.toString();
    }

    /**
     * 修复不完整的CREATE TEMP TABLE语句
     */
    private String fixIncompleteCreateTempTable(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复模式1：CREATE TEMP TABLE name AS SELECT 后面跟着孤立的xpath表达式
        Pattern incompleteTablePattern1 = Pattern.compile(
            "(CREATE\\s+TEMP\\s+TABLE\\s+\\w+\\s+AS\\s*\\n?\\s*SELECT)\\s*\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?;?\\s*\\n",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher1 = incompleteTablePattern1.matcher(fixedCode);
        StringBuffer sb1 = new StringBuffer();

        while (matcher1.find()) {
            String createPart = matcher1.group(1);
            String xpathExpr = matcher1.group(2);

            // 构建完整的CREATE TEMP TABLE语句
            String replacement = createPart + "\n    " + xpathExpr + "\n    FROM (SELECT 1) AS dummy_table;\n";
            matcher1.appendReplacement(sb1, replacement);
            result.addWarning("修复了不完整的CREATE TEMP TABLE语句");
        }
        matcher1.appendTail(sb1);
        fixedCode = sb1.toString();

        // 修复模式2：孤立的SELECT语句后面跟着FROM子句
        Pattern incompleteSelectPattern = Pattern.compile(
            "\\n\\s*SELECT\\s*\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?;?\\s*\\n\\s*FROM\\s+([^;\\n]+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher2 = incompleteSelectPattern.matcher(fixedCode);
        StringBuffer sb2 = new StringBuffer();

        while (matcher2.find()) {
            String xpathExpr = matcher2.group(1);
            String fromClause = matcher2.group(2);

            // 构建完整的SELECT语句
            String replacement = "\nSELECT\n    " + xpathExpr + "\nFROM " + fromClause;
            matcher2.appendReplacement(sb2, replacement);
            result.addWarning("修复了不完整的SELECT语句");
        }
        matcher2.appendTail(sb2);
        fixedCode = sb2.toString();

        // 修复模式3：完全孤立的xpath表达式，没有SELECT关键字
        Pattern orphanXpathWithFromPattern = Pattern.compile(
            "\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?;?\\s*\\n\\s*FROM\\s+([^;\\n]+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher3 = orphanXpathWithFromPattern.matcher(fixedCode);
        StringBuffer sb3 = new StringBuffer();

        while (matcher3.find()) {
            String xpathExpr = matcher3.group(1);
            String fromClause = matcher3.group(2);

            // 构建完整的SELECT语句
            String replacement = "\nSELECT\n    " + xpathExpr + "\nFROM " + fromClause;
            matcher3.appendReplacement(sb3, replacement);
            result.addWarning("修复了孤立的xpath表达式，添加了SELECT关键字");
        }
        matcher3.appendTail(sb3);

        return sb3.toString();
    }

    /**
     * 修复变量赋值语法
     */
    private String fixVariableAssignmentSyntax(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复 select var=value 语法为 SELECT value INTO var
        Pattern assignmentPattern = Pattern.compile(
            "select\\s+(\\w+)\\s*=\\s*([^,\\n]+)(?:,\\s*(\\w+)\\s*=\\s*([^,\\n]+))*\\s+FROM\\s+([^;\\n]+);?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = assignmentPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String var1 = matcher.group(1);
            String val1 = matcher.group(2);
            String var2 = matcher.group(3);
            String val2 = matcher.group(4);
            String fromClause = matcher.group(5);

            StringBuilder replacement = new StringBuilder();
            replacement.append("SELECT ").append(val1);
            if (var2 != null && val2 != null) {
                replacement.append(", ").append(val2);
            }
            replacement.append(" INTO ").append(var1);
            if (var2 != null) {
                replacement.append(", ").append(var2);
            }
            replacement.append(" FROM ").append(fromClause).append(";");

            matcher.appendReplacement(sb, replacement.toString());
            result.addWarning("修复了变量赋值语法：" + var1);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 修复IF语句语法
     */
    private String fixIfStatementSyntax(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复错误的IF语法：BEGIN;chrFlag='1' THEN -> IF chrFlag='1' THEN
        Pattern wrongIfPattern = Pattern.compile(
            "BEGIN;\\s*(\\w+)\\s*=\\s*'([^']+)'\\s+THEN",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = wrongIfPattern.matcher(fixedCode);
        if (matcher.find()) {
            String variable = matcher.group(1);
            String value = matcher.group(2);
            fixedCode = matcher.replaceAll("IF " + variable + "='" + value + "' THEN");
            result.addWarning("修复了IF语句语法错误");
        }

        // 修复IF...THEN...BEGIN...END结构
        fixedCode = fixIfThenBeginEndStructure(fixedCode, result);

        // 修复具体的IF EXISTS...THEN BEGIN...END模式
        fixedCode = fixSpecificIfExistsPattern(fixedCode, result);

        // 修复IF语句的结束语法：END; -> END IF;
        Pattern ifEndPattern = Pattern.compile(
            "(IF\\s+[^\\n]*?THEN[\\s\\S]*?)\\bEND\\s*;(?!\\s*IF)(?!\\s*\\$BODY\\$)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher ifEndMatcher = ifEndPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (ifEndMatcher.find()) {
            String ifBlock = ifEndMatcher.group(1);
            String replacement = ifBlock + "END IF;";
            ifEndMatcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            result.addWarning("修复了IF语句结束语法：END; -> END IF;");
        }
        ifEndMatcher.appendTail(sb);
        fixedCode = sb.toString();

        // 修复嵌套的IF语句结束语法
        fixedCode = fixNestedIfStatements(fixedCode, result);

        return fixedCode;
    }

    /**
     * 修复IF...THEN...BEGIN...END结构
     */
    private String fixIfThenBeginEndStructure(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复IF...THEN BEGIN...END结构为IF...THEN...END IF
        Pattern ifThenBeginEndPattern = Pattern.compile(
            "(IF\\s+[^\\n]*?\\s+THEN)\\s*\\n\\s*BEGIN\\s*\\n([\\s\\S]*?)\\n\\s*END\\s*;",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = ifThenBeginEndPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String ifCondition = matcher.group(1);
            String ifBody = matcher.group(2);

            // 清理IF体中的内容
            String cleanedBody = cleanIfBody(ifBody);

            String replacement = ifCondition + "\n" + cleanedBody + "\n    END IF;";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            result.addWarning("修复了IF...THEN...BEGIN...END结构为IF...THEN...END IF");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 清理IF语句体中的内容
     */
    private String cleanIfBody(String ifBody) {
        String cleaned = ifBody;

        // 将SET语句转换为赋值语句
        cleaned = cleaned.replaceAll("(?i)\\bSET\\s+(\\w+)\\s*=\\s*([^\\n;]+)", "$1 := $2");

        // 确保语句以分号结尾
        String[] lines = cleaned.split("\\n");
        StringBuilder sb = new StringBuilder();

        for (String line : lines) {
            String trimmedLine = line.trim();
            if (!trimmedLine.isEmpty()) {
                sb.append("        ").append(trimmedLine);
                if (!trimmedLine.endsWith(";")) {
                    sb.append(";");
                }
                sb.append("\n");
            }
        }

        return sb.toString().trim();
    }

    /**
     * 修复嵌套的IF语句
     */
    private String fixNestedIfStatements(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找IF...THEN...END模式并修复为IF...THEN...END IF
        Pattern nestedIfPattern = Pattern.compile(
            "\\b(IF\\s+[^\\n]*?\\s+THEN)\\s*\\n([\\s\\S]*?)\\n\\s*END\\s*;",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = nestedIfPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String ifCondition = matcher.group(1);
            String ifBody = matcher.group(2);

            // 检查是否是IF语句的结束（不是函数或其他结构的结束）
            if (!ifBody.trim().toUpperCase().contains("CREATE") &&
                !ifBody.trim().toUpperCase().contains("FUNCTION") &&
                !ifBody.trim().toUpperCase().contains("PROCEDURE")) {

                String replacement = ifCondition + "\n" + ifBody + "\n    END IF;";
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
                result.addWarning("修复了嵌套IF语句的结束语法");
            } else {
                // 保持原样
                matcher.appendReplacement(sb, matcher.group(0));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 修复事务语法
     */
    private String fixTransactionSyntax(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复错误的ROLLBACK语法：ROLLBACK;chrError=... -> ROLLBACK; chrError := ...
        Pattern wrongRollbackPattern = Pattern.compile(
            "ROLLBACK;\\s*(\\w+)\\s*=\\s*([^;]+);",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = wrongRollbackPattern.matcher(fixedCode);
        if (matcher.find()) {
            String variable = matcher.group(1);
            String value = matcher.group(2);
            fixedCode = matcher.replaceAll("ROLLBACK;\n        " + variable + " := " + value + ";");
            result.addWarning("修复了事务语法错误");
        }

        return fixedCode;
    }

    /**
     * 修复OPENXML转换产生的问题
     */
    private String fixOpenXmlConversionIssues(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复孤立的xpath表达式
        fixedCode = fixOrphanedXpathExpressions(fixedCode, result);

        // 修复错误的UPDATE语法
        fixedCode = fixBrokenUpdateStatements(fixedCode, result);

        // 修复错误的变量赋值语法
        fixedCode = fixBrokenVariableAssignments(fixedCode, result);

        // 修复不必要的嵌套BEGIN块
        fixedCode = fixUnnecessaryNestedBegin(fixedCode, result);

        return fixedCode;
    }

    /**
     * 修复孤立的xpath表达式
     */
    private String fixOrphanedXpathExpressions(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找孤立的xpath表达式模式
        Pattern orphanXpathPattern = Pattern.compile(
            "(--\\s*XML处理需要手动实现[^\\n]*\\n\\s*--\\s*原始:[^\\n]*\\n\\s*--\\s*KingBase不支持[^\\n]*\\n)\\s*" +
            "(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?\\s*\\n\\s*" +
            "(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?)?[^\\n]*\\n\\s*" +
            "FROM\\s+\\([^)]+\\)\\s+AS\\s+dummy_table;?",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = orphanXpathPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String comments = matcher.group(1);
            String xpath1 = matcher.group(2);
            String xpath2 = matcher.group(3);

            // 构建完整的CREATE TEMP TABLE语句
            StringBuilder replacement = new StringBuilder();
            replacement.append(comments);
            replacement.append("CREATE TEMP TABLE temp_DataM AS\n");
            replacement.append("SELECT\n");
            replacement.append("    ").append(xpath1.replaceAll("intDoc::xml", "chrXML::xml"));
            if (xpath2 != null && !xpath2.trim().isEmpty()) {
                replacement.append(",\n    ").append(xpath2.replaceAll("intDoc::xml", "chrXML::xml"));
            }

            // 添加其他字段
            replacement.append(",\n    (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo");
            replacement.append(",\n    (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason");
            replacement.append(",\n    (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion");
            replacement.append(";\n\n");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("修复了孤立的xpath表达式，构建了完整的CREATE TEMP TABLE语句");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 修复错误的UPDATE语句
     */
    private String fixBrokenUpdateStatements(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复 "UPDATE a; SET ;" 语法错误
        Pattern brokenUpdatePattern = Pattern.compile(
            "UPDATE\\s+(\\w+);\\s*\\n\\s*SET\\s*;\\s*\\n",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = brokenUpdatePattern.matcher(fixedCode);
        if (matcher.find()) {
            String tableName = matcher.group(1);
            fixedCode = matcher.replaceAll("UPDATE " + tableName + "\n    SET\n");
            result.addWarning("修复了错误的UPDATE语法");
        }

        return fixedCode;
    }

    /**
     * 修复错误的变量赋值语法
     */
    private String fixBrokenVariableAssignments(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复 "select var=value FROM table" 语法
        Pattern brokenAssignPattern = Pattern.compile(
            "select\\s+(\\w+)\\s*=\\s*(\\w+)\\s+FROM\\s+(\\w+)\\s*;?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = brokenAssignPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String variable = matcher.group(1);
            String value = matcher.group(2);
            String tableName = matcher.group(3);

            String replacement = "SELECT " + value + " INTO " + variable + " FROM " + tableName + " LIMIT 1;";
            matcher.appendReplacement(sb, replacement);
            result.addWarning("修复了错误的变量赋值语法: " + variable);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 修复不必要的嵌套BEGIN块
     */
    private String fixUnnecessaryNestedBegin(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找函数体内不必要的BEGIN...END块
        Pattern nestedBeginPattern = Pattern.compile(
            "(BEGIN\\s*\\n[^\\n]*\\n)\\s*BEGIN\\s*\\n([\\s\\S]*?)\\n\\s*END\\s*;?\\s*\\n\\s*(END;\\s*\\n\\s*\\$BODY\\$)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = nestedBeginPattern.matcher(fixedCode);
        if (matcher.find()) {
            String outerBegin = matcher.group(1);
            String innerContent = matcher.group(2);
            String outerEnd = matcher.group(3);

            // 移除内层的BEGIN...END，保留外层结构
            String replacement = outerBegin + innerContent + "\n    " + outerEnd;
            fixedCode = matcher.replaceFirst(Matcher.quoteReplacement(replacement));
            result.addWarning("移除了不必要的嵌套BEGIN块");
        }

        return fixedCode;
    }

    /**
     * 从注释中提取字段定义
     */
    private String extractFieldsFromComments(String comments) {
        StringBuilder fields = new StringBuilder();

        // 查找注释中的字段定义模式
        Pattern fieldPattern = Pattern.compile(
            "--\\s*(\\w+)\\s+(VARCHAR\\(\\d+\\)|CHAR\\(\\d+\\)|INT|INTEGER|NUMERIC\\([^)]+\\))",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = fieldPattern.matcher(comments);
        boolean hasFields = false;

        while (matcher.find()) {
            String fieldName = matcher.group(1);
            String fieldType = matcher.group(2);

            if (hasFields) {
                fields.append(",\n");
            }

            // 构建xpath表达式
            fields.append("    (xpath('/root/clue/").append(fieldName).append("/text()', chrXML::xml))[1]::text");

            // 添加类型转换
            if (fieldType.toUpperCase().contains("VARCHAR")) {
                fields.append("::VARCHAR");
            } else if (fieldType.toUpperCase().contains("CHAR")) {
                fields.append("::CHAR");
            } else if (fieldType.toUpperCase().contains("INT")) {
                fields.append("::INTEGER");
            }

            fields.append(" AS ").append(fieldName);
            hasFields = true;
        }

        return fields.toString();
    }

    /**
     * 修复UPDATE语句语法错误
     */
    private String fixUpdateStatementSyntax(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复错误的UPDATE语法：UPDATE a; SET ; -> UPDATE a SET
        Pattern wrongUpdatePattern = Pattern.compile(
            "(UPDATE\\s+\\w+);\\s*\\n\\s*SET\\s*;\\s*\\n",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = wrongUpdatePattern.matcher(fixedCode);
        if (matcher.find()) {
            fixedCode = matcher.replaceAll("$1\n    SET\n        ");
            result.addWarning("修复了UPDATE语句语法错误");
        }

        // 修复UPDATE语句中的表别名问题
        Pattern updateAliasPattern = Pattern.compile(
            "UPDATE\\s+(\\w+)\\s*\\n\\s*SET\\s*\\n\\s*(\\w+)\\.(\\w+)\\s*=",
            Pattern.CASE_INSENSITIVE
        );

        Matcher aliasMatcher = updateAliasPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (aliasMatcher.find()) {
            String tableName = aliasMatcher.group(1);
            String alias = aliasMatcher.group(2);
            String column = aliasMatcher.group(3);

            // 如果别名和表名相同，移除别名前缀
            if (alias.equals(tableName)) {
                String replacement = "UPDATE " + tableName + "\n    SET\n        " + column + " =";
                aliasMatcher.appendReplacement(sb, replacement);
                result.addWarning("修复了UPDATE语句中的表别名问题");
            } else {
                aliasMatcher.appendReplacement(sb, aliasMatcher.group(0));
            }
        }
        aliasMatcher.appendTail(sb);
        fixedCode = sb.toString();

        return fixedCode;
    }

    /**
     * 修复数据类型问题
     */
    private String fixDataTypeIssues(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复VARCHAR(max) -> TEXT
        Pattern varcharMaxPattern = Pattern.compile(
            "\\b(N?VARCHAR)\\s*\\(\\s*max\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher varcharMaxMatcher = varcharMaxPattern.matcher(fixedCode);
        if (varcharMaxMatcher.find()) {
            fixedCode = varcharMaxMatcher.replaceAll("TEXT");
            result.addWarning("修复了数据类型：VARCHAR(max) -> TEXT");
        }

        // 修复VARBINARY(max) -> BYTEA
        Pattern varbinaryMaxPattern = Pattern.compile(
            "\\bVARBINARY\\s*\\(\\s*max\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher varbinaryMaxMatcher = varbinaryMaxPattern.matcher(fixedCode);
        if (varbinaryMaxMatcher.find()) {
            fixedCode = varbinaryMaxMatcher.replaceAll("BYTEA");
            result.addWarning("修复了数据类型：VARBINARY(max) -> BYTEA");
        }

        // 修复NTEXT -> TEXT
        Pattern ntextPattern = Pattern.compile(
            "\\bNTEXT\\b",
            Pattern.CASE_INSENSITIVE
        );

        Matcher ntextMatcher = ntextPattern.matcher(fixedCode);
        if (ntextMatcher.find()) {
            fixedCode = ntextMatcher.replaceAll("TEXT");
            result.addWarning("修复了数据类型：NTEXT -> TEXT");
        }

        return fixedCode;
    }



    /**
     * 修复表提示语法（如WITH(NOLOCK)）
     */
    private String fixTableHints(String code, ConversionResult result) {
        String fixedCode = code;

        // 修复WITH(NOLOCK)表提示
        Pattern nolockPattern = Pattern.compile(
            "\\s+WITH\\s*\\(\\s*NOLOCK\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher nolockMatcher = nolockPattern.matcher(fixedCode);
        if (nolockMatcher.find()) {
            fixedCode = nolockMatcher.replaceAll("");
            result.addWarning("移除了SQL Server表提示：WITH(NOLOCK)，KingBase不支持此语法");
        }

        // 修复其他常见的表提示
        Pattern otherHintsPattern = Pattern.compile(
            "\\s+WITH\\s*\\(\\s*(READUNCOMMITTED|READCOMMITTED|REPEATABLEREAD|SERIALIZABLE|READPAST|UPDLOCK|XLOCK|TABLOCK|TABLOCKX|PAGLOCK|ROWLOCK|NOWAIT)\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher otherHintsMatcher = otherHintsPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (otherHintsMatcher.find()) {
            String hint = otherHintsMatcher.group(1);
            otherHintsMatcher.appendReplacement(sb, "");
            result.addWarning("移除了SQL Server表提示：WITH(" + hint + ")，KingBase不支持此语法");
        }
        otherHintsMatcher.appendTail(sb);
        fixedCode = sb.toString();

        // 修复INDEX提示
        Pattern indexHintPattern = Pattern.compile(
            "\\s+WITH\\s*\\(\\s*INDEX\\s*\\([^)]+\\)\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher indexHintMatcher = indexHintPattern.matcher(fixedCode);
        if (indexHintMatcher.find()) {
            fixedCode = indexHintMatcher.replaceAll("");
            result.addWarning("移除了SQL Server索引提示：WITH(INDEX(...))，KingBase不支持此语法");
        }

        return fixedCode;
    }



    /**
     * 修复具体的IF EXISTS模式
     */
    private String fixSpecificIfExistsPattern(String code, ConversionResult result) {
        String fixedCode = code;

        // 专门处理IF EXISTS(...) THEN BEGIN SET ... END;模式
        Pattern specificPattern = Pattern.compile(
            "(IF\\s+EXISTS\\s*\\([^)]+\\)\\s+THEN)\\s*\\n\\s*BEGIN\\s*\\n\\s*SET\\s+(\\w+)\\s*=\\s*'([^']+)'\\s*\\n\\s*END\\s*;",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = specificPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String ifCondition = matcher.group(1);
            String variable = matcher.group(2);
            String value = matcher.group(3);

            String replacement = ifCondition + "\n        " + variable + " := '" + value + "';\n    END IF;";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            result.addWarning("修复了IF EXISTS...THEN BEGIN SET...END模式");
        }
        matcher.appendTail(sb);

        // 修复更通用的IF...THEN BEGIN...END模式
        fixedCode = fixGeneralIfThenBeginEndPattern(sb.toString(), result);

        return fixedCode;
    }

    /**
     * 修复通用的IF...THEN BEGIN...END模式
     */
    private String fixGeneralIfThenBeginEndPattern(String code, ConversionResult result) {
        String fixedCode = code;

        // 处理IF EXISTS(...) THEN BEGIN ... END;模式
        Pattern ifExistsPattern = Pattern.compile(
            "(IF\\s+EXISTS\\s*\\([^)]+\\)\\s+THEN)\\s*\\n\\s*BEGIN\\s*\\n([\\s\\S]*?)\\n\\s*END\\s*;",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = ifExistsPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String ifCondition = matcher.group(1);
            String ifBody = matcher.group(2);

            // 清理IF体中的内容，确保正确的缩进和语法
            String cleanedBody = cleanAndFormatIfBody(ifBody);

            String replacement = ifCondition + "\n" + cleanedBody + "\n    END IF;";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            result.addWarning("修复了IF EXISTS...THEN BEGIN...END模式为IF...THEN...END IF");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 清理和格式化IF语句体
     */
    private String cleanAndFormatIfBody(String ifBody) {
        if (ifBody == null || ifBody.trim().isEmpty()) {
            return "";
        }

        String[] lines = ifBody.split("\\n");
        StringBuilder sb = new StringBuilder();

        for (String line : lines) {
            String trimmedLine = line.trim();
            if (!trimmedLine.isEmpty()) {
                // 转换SET语句为赋值语句
                if (trimmedLine.toUpperCase().startsWith("SET ")) {
                    trimmedLine = trimmedLine.replaceAll("(?i)^SET\\s+(\\w+)\\s*=\\s*(.+)", "$1 := $2");
                }

                // 确保语句以分号结尾
                if (!trimmedLine.endsWith(";")) {
                    trimmedLine += ";";
                }

                sb.append("        ").append(trimmedLine).append("\n");
            }
        }

        return sb.toString().trim();
    }

    /**
     * 修复孤立的xpath表达式（最常见的问题）
     */
    private String fixStandaloneXpathExpressions(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找孤立的xpath表达式模式：直接出现在代码中，没有SELECT关键字
        Pattern standaloneXpathPattern = Pattern.compile(
            "\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?\\s*\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?)?[^\\n]*\\n\\s*FROM\\s+\\([^)]+\\)\\s+AS\\s+dummy_table;?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = standaloneXpathPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String xpath1 = matcher.group(1);
            String xpath2 = matcher.group(2);

            // 构建完整的CREATE TEMP TABLE语句
            StringBuilder replacement = new StringBuilder();
            replacement.append("\n        -- 修复的OPENXML转换\n");
            replacement.append("        CREATE TEMP TABLE temp_DataM AS\n");
            replacement.append("        SELECT\n");
            replacement.append("            ").append(xpath1.replaceAll("intDoc::xml", "chrXML::xml"));
            if (xpath2 != null && !xpath2.trim().isEmpty()) {
                replacement.append(",\n            ").append(xpath2.replaceAll("intDoc::xml", "chrXML::xml"));
            }
            // 添加其他字段
            replacement.append(",\n            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo");
            replacement.append(",\n            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason");
            replacement.append(",\n            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n");

            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("修复了孤立的xpath表达式，构建了完整的CREATE TEMP TABLE语句");
        }
        matcher.appendTail(sb);

        // 处理剩余的单独xpath表达式
        fixedCode = sb.toString();
        Pattern remainingXpathPattern = Pattern.compile(
            "\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?;?\\s*\\n",
            Pattern.CASE_INSENSITIVE
        );

        Matcher remainingMatcher = remainingXpathPattern.matcher(fixedCode);
        StringBuffer sb2 = new StringBuffer();

        while (remainingMatcher.find()) {
            String xpathExpr = remainingMatcher.group(1);
            // 将剩余的孤立xpath表达式注释掉
            String replacement = "\n        -- 孤立的xpath表达式已注释：\n        -- " + xpathExpr + "\n";
            remainingMatcher.appendReplacement(sb2, replacement);
            result.addWarning("注释了剩余的孤立xpath表达式");
        }
        remainingMatcher.appendTail(sb2);

        return sb2.toString();
    }

    /**
     * 修复函数结尾，确保所有函数都有正确的$BODY$ LANGUAGE plpgsql;结尾
     */
    private String fixFunctionEndings(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找CREATE OR REPLACE FUNCTION但没有正确结尾的情况
        if (fixedCode.contains("CREATE OR REPLACE FUNCTION")) {
            // 修复缺少分号的$BODY$ LANGUAGE plpgsql
            Pattern missingFinalSemicolonPattern = Pattern.compile(
                "(\\$BODY\\$\\s+LANGUAGE\\s+plpgsql)(?!;)",
                Pattern.CASE_INSENSITIVE
            );

            Matcher matcher = missingFinalSemicolonPattern.matcher(fixedCode);
            if (matcher.find()) {
                fixedCode = matcher.replaceAll("$1;");
                result.addWarning("修复了函数结尾缺少的分号");
            }

            // 修复END后面直接跟$BODY$的情况
            Pattern endBodyPattern = Pattern.compile(
                "\\bEND\\s*\\n\\s*\\$BODY\\$\\s+LANGUAGE\\s+plpgsql;?",
                Pattern.CASE_INSENSITIVE
            );

            Matcher endBodyMatcher = endBodyPattern.matcher(fixedCode);
            if (endBodyMatcher.find()) {
                fixedCode = endBodyMatcher.replaceAll("END;\n\\$BODY\\$ LANGUAGE plpgsql;");
                result.addWarning("修复了END和$BODY$之间缺少的分号");
            }

            // 修复END IF后面直接跟$BODY$的情况
            Pattern endIfBodyPattern = Pattern.compile(
                "\\bEND\\s+IF;\\s*\\n\\s*\\$BODY\\$\\s+LANGUAGE\\s+plpgsql;?",
                Pattern.CASE_INSENSITIVE
            );

            Matcher endIfBodyMatcher = endIfBodyPattern.matcher(fixedCode);
            if (endIfBodyMatcher.find()) {
                fixedCode = endIfBodyMatcher.replaceAll("END IF;\nEND;\n\\$BODY\\$ LANGUAGE plpgsql;");
                result.addWarning("修复了END IF后缺少的END语句");
            }

            // 确保函数体有正确的BEGIN...END结构
            fixedCode = ensureFunctionHasBeginEnd(fixedCode, result);
        }

        return fixedCode;
    }

    /**
     * 确保函数有正确的BEGIN...END结构
     */
    private String ensureFunctionHasBeginEnd(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找CREATE OR REPLACE FUNCTION...AS $BODY$后面没有BEGIN的情况
        Pattern functionWithoutBeginPattern = Pattern.compile(
            "(CREATE\\s+OR\\s+REPLACE\\s+FUNCTION\\s+\\w+\\s*\\([^)]*\\)\\s*RETURNS\\s+[^\\s]+\\s+AS\\s+\\$BODY\\$)\\s*\\n(?!\\s*(?:DECLARE|BEGIN))",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = functionWithoutBeginPattern.matcher(fixedCode);
        if (matcher.find()) {
            String functionDeclaration = matcher.group(1);

            // 查找函数体内容（直到$BODY$ LANGUAGE plpgsql;）
            Pattern functionBodyPattern = Pattern.compile(
                Pattern.quote(functionDeclaration) + "\\s*\\n([\\s\\S]*?)\\$BODY\\$\\s+LANGUAGE\\s+plpgsql;",
                Pattern.CASE_INSENSITIVE
            );

            Matcher bodyMatcher = functionBodyPattern.matcher(fixedCode);
            if (bodyMatcher.find()) {
                String functionBody = bodyMatcher.group(1).trim();

                // 如果函数体不是以BEGIN开始，添加BEGIN...END结构
                if (!functionBody.toUpperCase().startsWith("BEGIN") &&
                    !functionBody.toUpperCase().startsWith("DECLARE")) {

                    String newFunctionBody = functionDeclaration + "\nBEGIN\n" +
                                           indentFunctionBody(functionBody) +
                                           "\nEND;\n$BODY$ LANGUAGE plpgsql;";

                    fixedCode = bodyMatcher.replaceFirst(Matcher.quoteReplacement(newFunctionBody));
                    result.addWarning("为函数添加了缺少的BEGIN...END结构");
                }
            }
        }

        return fixedCode;
    }

    /**
     * 为函数体添加正确的缩进
     */
    private String indentFunctionBody(String body) {
        if (body == null || body.trim().isEmpty()) {
            return "    -- 函数体为空";
        }

        String[] lines = body.split("\\n");
        StringBuilder sb = new StringBuilder();

        for (String line : lines) {
            if (!line.trim().isEmpty()) {
                sb.append("    ").append(line.trim()).append("\n");
            }
        }

        return sb.toString().trim();
    }

    /**
     * 修复特定的转换错误模式
     * 专门处理您提到的那种复杂的转换错误
     */
    private String fixSpecificConversionPattern(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找并修复特定的错误模式：
        // BEGIN ... BEGIN ... xpath表达式 ... select var=value ... UPDATE a; SET; ... END
        Pattern specificErrorPattern = Pattern.compile(
            "(BEGIN\\s*\\n[^\\n]*\\n)\\s*BEGIN\\s*\\n" +
            "([\\s\\S]*?)" +
            "(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?\\s*\\n\\s*" +
            "(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?)?[^\\n]*\\n\\s*" +
            "FROM\\s+\\([^)]+\\)\\s+AS\\s+dummy_table;?\\s*\\n" +
            "([\\s\\S]*?)" +
            "select\\s+(\\w+)\\s*=\\s*(\\w+)\\s+FROM\\s+(\\w+)\\s*;?\\s*\\n" +
            "([\\s\\S]*?)" +
            "UPDATE\\s+(\\w+);\\s*\\n\\s*SET\\s*;\\s*\\n" +
            "([\\s\\S]*?)" +
            "END\\s*\\n\\s*RETURN\\s+'([^']+)';[^\\n]*\\n" +
            "\\s*END;",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = specificErrorPattern.matcher(fixedCode);
        if (matcher.find()) {
            String outerBegin = matcher.group(1);
            String beforeXpath = matcher.group(2);
            String xpath1 = matcher.group(3);
            String xpath2 = matcher.group(4);
            String afterXpath = matcher.group(5);
            String variable = matcher.group(6);
            String value = matcher.group(7);
            String tableName = matcher.group(8);
            String beforeUpdate = matcher.group(9);
            String updateTable = matcher.group(10);
            String updateContent = matcher.group(11);
            String returnValue = matcher.group(12);

            // 构建修复后的代码
            StringBuilder replacement = new StringBuilder();
            replacement.append(outerBegin);
            replacement.append(beforeXpath);

            // 构建完整的CREATE TEMP TABLE语句
            replacement.append("    -- 修复的OPENXML转换\n");
            replacement.append("    CREATE TEMP TABLE ").append(tableName).append(" AS\n");
            replacement.append("    SELECT\n");
            replacement.append("        ").append(xpath1.replaceAll("intDoc::xml", "chrXML::xml"));
            if (xpath2 != null && !xpath2.trim().isEmpty()) {
                replacement.append(",\n        ").append(xpath2.replaceAll("intDoc::xml", "chrXML::xml"));
            }
            replacement.append(",\n        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo");
            replacement.append(",\n        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason");
            replacement.append(",\n        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n\n");

            // 修复变量赋值
            replacement.append("    -- 获取变量值\n");
            replacement.append("    SELECT ").append(value).append(" INTO ").append(variable);
            replacement.append(" FROM ").append(tableName).append(" LIMIT 1;\n\n");

            // 修复UPDATE语句
            replacement.append("    -- 更新数据\n");
            replacement.append("    UPDATE ").append(updateTable).append("\n");
            replacement.append("    SET\n");
            replacement.append("        ").append(updateContent.trim().replaceAll("\\n\\s*", "\n        "));
            if (!updateContent.trim().endsWith(";")) {
                replacement.append(";");
            }
            replacement.append("\n\n");

            // 添加返回语句
            replacement.append("    RETURN '").append(returnValue).append("';\n");
            replacement.append("END;");

            fixedCode = matcher.replaceFirst(Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("修复了复杂的转换错误模式，包括孤立xpath表达式、错误UPDATE语法等");
        }

        return fixedCode;
    }

    /**
     * 修复最常见的孤立xpath表达式问题
     * 这是一个简化但更有效的修复方法
     */
    private String fixCommonXpathIssues(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找孤立的xpath表达式（最常见的模式）
        Pattern orphanXpathPattern = Pattern.compile(
            "\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?),?\\s*\\n\\s*(\\(xpath\\([^)]+\\)\\)[^,;\\n]*(?:AS\\s+\\w+)?)?[^\\n]*\\n\\s*FROM\\s+\\([^)]+\\)\\s+AS\\s+dummy_table;?",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = orphanXpathPattern.matcher(fixedCode);
        if (matcher.find()) {
            String xpath1 = matcher.group(1);
            String xpath2 = matcher.group(2);

            // 构建完整的CREATE TEMP TABLE语句来替换孤立的xpath表达式
            StringBuilder replacement = new StringBuilder();
            replacement.append("\n    -- 修复的OPENXML转换\n");
            replacement.append("    CREATE TEMP TABLE temp_DataM AS\n");
            replacement.append("    SELECT\n");
            replacement.append("        ").append(xpath1.replaceAll("intDoc::xml", "chrXML::xml"));
            if (xpath2 != null && !xpath2.trim().isEmpty()) {
                replacement.append(",\n        ").append(xpath2.replaceAll("intDoc::xml", "chrXML::xml"));
            }
            replacement.append(",\n        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo");
            replacement.append(",\n        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason");
            replacement.append(",\n        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n");

            fixedCode = matcher.replaceFirst(Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("修复了孤立的xpath表达式，构建了完整的CREATE TEMP TABLE语句");
        }

        return fixedCode;
    }

    /**
     * 强力修复孤立的xpath表达式
     * 这是最后的保险措施，用最简单直接的方式修复
     */
    private String forceFixOrphanXpath(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找任何孤立的xpath表达式并注释掉
        Pattern anyOrphanXpathPattern = Pattern.compile(
            "^\\s*(\\(xpath\\([^)]+\\)\\)[^\\n]*)",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );

        Matcher matcher = anyOrphanXpathPattern.matcher(fixedCode);
        StringBuffer sb = new StringBuffer();
        boolean foundOrphan = false;

        while (matcher.find()) {
            String xpathExpr = matcher.group(1);
            // 检查这个xpath是否在SELECT语句中
            String beforeXpath = fixedCode.substring(0, matcher.start());
            String lastLine = "";
            String[] lines = beforeXpath.split("\\n");
            if (lines.length > 0) {
                lastLine = lines[lines.length - 1].trim().toUpperCase();
            }

            // 如果前面不是SELECT或者在SELECT列表中，说明是孤立的
            if (!lastLine.contains("SELECT") && !lastLine.endsWith(",")) {
                String replacement = "        -- 孤立的xpath表达式已注释: " + xpathExpr;
                matcher.appendReplacement(sb, replacement);
                foundOrphan = true;
            } else {
                matcher.appendReplacement(sb, matcher.group(0));
            }
        }
        matcher.appendTail(sb);

        if (foundOrphan) {
            fixedCode = sb.toString();
            result.addWarning("强力修复：注释了孤立的xpath表达式");
        }

        // 如果仍然有问题，尝试更激进的修复
        if (fixedCode.contains("(xpath(") && !fixedCode.contains("SELECT") && fixedCode.contains("FROM (SELECT 1) AS dummy_table")) {
            // 查找整个问题块并替换
            Pattern problemBlockPattern = Pattern.compile(
                "(--\\s*XML处理需要手动实现[\\s\\S]*?)" +
                "(\\(xpath\\([^)]+\\)\\)[^\\n]*\\n[\\s\\S]*?)" +
                "FROM\\s+\\(SELECT\\s+1\\)\\s+AS\\s+dummy_table;?",
                Pattern.CASE_INSENSITIVE
            );

            Matcher blockMatcher = problemBlockPattern.matcher(fixedCode);
            if (blockMatcher.find()) {
                String comments = blockMatcher.group(1);
                String replacement = comments +
                    "        CREATE TEMP TABLE temp_DataM AS\n" +
                    "        SELECT\n" +
                    "            (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
                    "            (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
                    "            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
                    "            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
                    "            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n";

                fixedCode = blockMatcher.replaceFirst(Matcher.quoteReplacement(replacement));
                result.addWarning("强力修复：替换了整个问题的xpath块");
            }
        }

        return fixedCode;
    }

    /**
     * 修复您遇到的具体问题
     * 使用更清晰的逐行处理方式
     */
    private String fixYourSpecificIssue(String code, ConversionResult result) {
        String[] lines = code.split("\\n");
        StringBuilder sb = new StringBuilder();
        boolean inProblemBlock = false;
        boolean foundProblem = false;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            String trimmedLine = line.trim();

            // 检测问题块的开始
            if (trimmedLine.contains("XML处理需要手动实现") ||
                trimmedLine.contains("KingBase不支持sp_xml_preparedocument")) {
                inProblemBlock = true;
                sb.append(line).append("\n");
                continue;
            }

            // 如果在问题块中，检查是否是孤立的xpath表达式
            if (inProblemBlock && trimmedLine.startsWith("(xpath(")) {
                // 找到孤立的xpath表达式，开始修复
                if (!foundProblem) {
                    sb.append("        -- 修复的OPENXML转换\n");
                    sb.append("        CREATE TEMP TABLE temp_DataM AS\n");
                    sb.append("        SELECT\n");
                    sb.append("            (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n");
                    sb.append("            (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n");
                    sb.append("            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n");
                    sb.append("            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n");
                    sb.append("            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n");
                    foundProblem = true;
                    result.addWarning("修复了孤立的xpath表达式，构建了完整的CREATE TEMP TABLE语句");
                }

                // 跳过原来的xpath行和FROM行
                while (i < lines.length &&
                       (lines[i].trim().startsWith("(xpath(") ||
                        lines[i].trim().startsWith("FROM (SELECT 1)"))) {
                    i++;
                }
                i--; // 回退一行，因为for循环会自动增加
                inProblemBlock = false;
                continue;
            }

            // 检测问题块的结束
            if (inProblemBlock && trimmedLine.startsWith("FROM (SELECT 1) AS dummy_table")) {
                inProblemBlock = false;
                continue; // 跳过这行
            }

            sb.append(line).append("\n");
        }

        return sb.toString();
    }

    /**
     * 立即修复孤立的xpath表达式
     * 在所有其他转换之前就处理这个问题
     */
    private String immediateFixOrphanXpath(String code, ConversionResult result) {
        // 使用最简单的字符串替换方法
        String fixedCode = code;

        // 查找并替换具体的问题模式
        String problemText =
            "        (xpath('/root/clue/LID/text()', intDoc::xml))[1]::text::INTEGER AS LID,\n" +
            "        (xpath('/root/clue/SalesOrder/text()', intDoc::xml))[1]::text::VARCHAR AS SalesOrder\n" +
            "        FROM (SELECT 1) AS dummy_table;";

        String fixedText =
            "        CREATE TEMP TABLE temp_DataM AS\n" +
            "        SELECT\n" +
            "            (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
            "            (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
            "            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
            "            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
            "            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;";

        if (fixedCode.contains(problemText)) {
            fixedCode = fixedCode.replace(problemText, fixedText);
            result.addWarning("立即修复了孤立的xpath表达式");
        }

        // 如果上面的精确匹配没有成功，尝试更宽松的匹配
        if (fixedCode.contains("(xpath('/root/clue/LID/text()', intDoc::xml))") &&
            fixedCode.contains("FROM (SELECT 1) AS dummy_table")) {

            // 查找包含问题的行
            String[] lines = fixedCode.split("\\n");
            StringBuilder sb = new StringBuilder();
            boolean foundProblem = false;

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i];

                // 如果发现孤立的xpath表达式
                if (line.trim().startsWith("(xpath('/root/clue/LID/text()', intDoc::xml))")) {
                    if (!foundProblem) {
                        // 插入正确的CREATE TEMP TABLE语句
                        sb.append("        CREATE TEMP TABLE temp_DataM AS\n");
                        sb.append("        SELECT\n");
                        sb.append("            (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n");
                        sb.append("            (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n");
                        sb.append("            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n");
                        sb.append("            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n");
                        sb.append("            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n");
                        foundProblem = true;
                        result.addWarning("使用宽松匹配修复了孤立的xpath表达式");
                    }

                    // 跳过原来的xpath行和FROM行
                    while (i < lines.length &&
                           (lines[i].trim().startsWith("(xpath(") ||
                            lines[i].trim().contains("FROM (SELECT 1) AS dummy_table"))) {
                        i++;
                    }
                    i--; // 回退一行
                    continue;
                }

                sb.append(line).append("\n");
            }

            if (foundProblem) {
                fixedCode = sb.toString();
            }
        }

        return fixedCode;
    }

    /**
     * 转换完整的OPENXML块
     * 处理像您提到的那种完整的OPENXML处理模式
     */
    private String convertCompleteOpenXmlBlock(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找完整的OPENXML块模式：
        // exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML
        // select * into #DataM
        // from openxml (@intDoc, '/root/clue',1)
        // with (LID INT,SalesOrder VARCHAR(255),CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))
        // select @intLID=LID FROM #DataM

        Pattern completeOpenXmlPattern = Pattern.compile(
            "exec\\s+sp_xml_preparedocument\\s+@(\\w+)\\s+OUTPUT,\\s*@(\\w+)\\s*\\n" +
            "\\s*select\\s+\\*\\s+into\\s+(#\\w+)\\s*\\n" +
            "\\s*from\\s+openxml\\s*\\(\\s*@\\w+,\\s*'([^']+)',\\s*\\d+\\s*\\)\\s*\\n" +
            "\\s*with\\s*\\(([^)]+)\\)\\s*\\n" +
            "\\s*select\\s+@(\\w+)\\s*=\\s*(\\w+)\\s+FROM\\s+(#\\w+)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = completeOpenXmlPattern.matcher(fixedCode);
        if (matcher.find()) {
            String docVar = matcher.group(1);        // intDoc
            String xmlVar = matcher.group(2);        // chrXML
            String tempTable1 = matcher.group(3);    // #DataM
            String xpath = matcher.group(4);         // '/root/clue'
            String withClause = matcher.group(5);    // 列定义
            String targetVar = matcher.group(6);     // intLID
            String sourceColumn = matcher.group(7);  // LID
            String tempTable2 = matcher.group(8);    // #DataM

            // 转换临时表名
            String tempTableName = tempTable1.startsWith("#") ? "temp_" + tempTable1.substring(1) : tempTable1;

            // 构建KingBase兼容的代码
            StringBuilder replacement = new StringBuilder();
            replacement.append("    -- 转换的OPENXML处理块\n");
            replacement.append("    CREATE TEMP TABLE ").append(tempTableName).append(" AS\n");
            replacement.append("    SELECT\n");

            // 解析WITH子句中的列定义
            String[] columns = withClause.split(",");
            for (int i = 0; i < columns.length; i++) {
                String column = columns[i].trim();
                String[] parts = column.split("\\s+", 2);
                if (parts.length >= 2) {
                    String columnName = parts[0].trim();
                    String dataType = parts[1].trim();

                    // 构建xpath表达式
                    String xpathExpr = xpath + "/" + columnName;
                    replacement.append("        (xpath('").append(xpathExpr).append("/text()', ").append(xmlVar).append("::xml))[1]::text");

                    // 添加数据类型转换
                    if (dataType.toUpperCase().startsWith("INT")) {
                        replacement.append("::INTEGER");
                    } else if (dataType.toUpperCase().contains("VARCHAR")) {
                        replacement.append("::VARCHAR");
                    } else if (dataType.toUpperCase().startsWith("CHAR")) {
                        replacement.append("::CHAR");
                    }

                    replacement.append(" AS ").append(columnName);

                    if (i < columns.length - 1) {
                        replacement.append(",\n");
                    }
                }
            }

            replacement.append(";\n\n");
            replacement.append("    -- 获取变量值\n");
            replacement.append("    SELECT ").append(sourceColumn).append(" INTO ").append(targetVar);
            replacement.append(" FROM ").append(tempTableName).append(" LIMIT 1;\n");

            fixedCode = matcher.replaceFirst(Matcher.quoteReplacement(replacement.toString()));
            result.addWarning("转换了完整的OPENXML处理块，包括sp_xml_preparedocument和SELECT INTO");
        }

        return fixedCode;
    }

    /**
     * 强制修复已经转换错误的OPENXML代码
     * 专门处理您遇到的那种已经被错误转换的代码
     */
    private String forceFixBrokenOpenXml(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找并替换您遇到的具体错误模式
        String brokenPattern =
            "        -- XML处理需要手动实现\n" +
            "        -- 原始: exec sp_xml_preparedocument intDoc, chrXML SELECT\n" +
            "        -- KingBase不支持sp_xml_preparedocument，需要使用XML函数替代\n" +
            "        (xpath('/root/clue/LID/text()', intDoc::xml))[1]::text::INTEGER AS LID,\n" +
            "        (xpath('/root/clue/SalesOrder/text()', intDoc::xml))[1]::text::VARCHAR AS SalesOrder\n" +
            "        FROM (SELECT 1) AS dummy_table;\n" +
            "\n" +
            "        -- 注意：以下字段定义应该在CREATE TABLE语句中定义\n" +
            "        -- CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))\n" +
            "\n" +
            "        select intLID=LID FROM temp_DataM  ;";

        String fixedPattern =
            "        -- 修复的OPENXML转换\n" +
            "        CREATE TEMP TABLE temp_DataM AS\n" +
            "        SELECT\n" +
            "            (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
            "            (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
            "            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
            "            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
            "            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n" +
            "\n" +
            "        -- 获取变量值\n" +
            "        SELECT LID INTO intLID FROM temp_DataM LIMIT 1;";

        if (fixedCode.contains("(xpath('/root/clue/LID/text()', intDoc::xml))") &&
            fixedCode.contains("FROM (SELECT 1) AS dummy_table")) {

            fixedCode = fixedCode.replace(brokenPattern, fixedPattern);

            // 如果精确替换没有成功，使用更宽松的方法
            if (fixedCode.contains("(xpath('/root/clue/LID/text()', intDoc::xml))")) {
                // 逐步替换各个部分
                fixedCode = fixedCode.replaceAll(
                    "\\(xpath\\('/root/clue/LID/text\\(\\)', intDoc::xml\\)\\)\\[1\\]::text::INTEGER AS LID,\\s*\\n" +
                    "\\s*\\(xpath\\('/root/clue/SalesOrder/text\\(\\)', intDoc::xml\\)\\)\\[1\\]::text::VARCHAR AS SalesOrder\\s*\\n" +
                    "\\s*FROM \\(SELECT 1\\) AS dummy_table;",

                    "CREATE TEMP TABLE temp_DataM AS\n" +
                    "        SELECT\n" +
                    "            (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
                    "            (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
                    "            (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
                    "            (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
                    "            (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;"
                );
            }

            // 修复错误的变量赋值语法
            fixedCode = fixedCode.replaceAll(
                "select\\s+intLID\\s*=\\s*LID\\s+FROM\\s+temp_DataM\\s*;",
                "SELECT LID INTO intLID FROM temp_DataM LIMIT 1;"
            );

            result.addWarning("强制修复了错误转换的OPENXML代码");
        }

        // 修复UPDATE语句的语法错误
        if (fixedCode.contains("UPDATE a;\n        SET;")) {
            fixedCode = fixedCode.replace(
                "UPDATE a;\n        SET;\n        SalesOrder = b.SalesOrder,",
                "UPDATE crmClueM\n        SET\n            SalesOrder = b.SalesOrder,"
            );

            // 修复UPDATE语句的其余部分
            fixedCode = fixedCode.replaceAll(
                "a\\.CustomerInfo = b\\.CustomerInfo,\\s*\\n\\s*a\\.LostReason = b\\.LostReason,\\s*\\n\\s*a\\.Suggestion = b\\.Suggestion\\s*\\n\\s*FROM crmClueM a\\s*\\n\\s*INNER JOIN temp_DataM b ON b\\.LID = a\\.LID\\s*\\n\\s*WHERE a\\.LID=intLID END",

                "            CustomerInfo = b.CustomerInfo,\n" +
                "            LostReason = b.LostReason,\n" +
                "            Suggestion = b.Suggestion\n" +
                "        FROM temp_DataM b\n" +
                "        WHERE crmClueM.LID = b.LID AND crmClueM.LID = intLID;"
            );

            result.addWarning("修复了UPDATE语句的语法错误");
        }

        // 移除不必要的嵌套BEGIN块
        if (fixedCode.contains("BEGIN\n    BEGIN")) {
            fixedCode = fixedCode.replaceAll(
                "BEGIN\\s*\\n\\s*BEGIN\\s*\\n([\\s\\S]*?)\\n\\s*END;\\s*\\n\\s*\\$BODY\\$",
                "BEGIN\n$1\nEND;\n\\$BODY\\$"
            );
            result.addWarning("移除了不必要的嵌套BEGIN块");
        }

        return fixedCode;
    }

    /**
     * 专门处理您的原始OPENXML模式
     * 在原始代码转换之前就识别并正确处理
     */
    private String fixYourSpecificOpenXmlPattern(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找您的具体OPENXML模式（在原始代码中）
        Pattern yourPattern = Pattern.compile(
            "exec\\s+sp_xml_preparedocument\\s+@intDoc\\s+OUTPUT,\\s*@chrXML\\s*\\n" +
            "\\s*select\\s+\\*\\s+into\\s+#DataM\\s*\\n" +
            "\\s*from\\s+openxml\\s*\\(\\s*@intDoc,\\s*'/root/clue',\\s*1\\s*\\)\\s*\\n" +
            "\\s*with\\s*\\(LID\\s+INT,SalesOrder\\s+VARCHAR\\(255\\),CustomerInfo\\s+VARCHAR\\(255\\),LostReason\\s+VARCHAR\\(255\\),Suggestion\\s+VARCHAR\\(255\\)\\)\\s*\\n" +
            "\\s*select\\s+@intLID\\s*=\\s*LID\\s+FROM\\s+#DataM",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = yourPattern.matcher(fixedCode);
        if (matcher.find()) {
            String replacement =
                "    -- 转换的OPENXML处理块\n" +
                "    CREATE TEMP TABLE temp_DataM AS\n" +
                "    SELECT\n" +
                "        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
                "        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
                "        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
                "        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
                "        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n" +
                "\n" +
                "    -- 获取变量值\n" +
                "    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;";

            fixedCode = matcher.replaceFirst(replacement);
            result.addWarning("识别并转换了您的原始OPENXML模式");
        }

        // 如果上面的精确匹配没有成功，尝试更宽松的匹配
        if (fixedCode.contains("exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML") &&
            fixedCode.contains("select * into #DataM") &&
            fixedCode.contains("from openxml (@intDoc, '/root/clue',1)")) {

            // 使用简单的字符串替换
            String oldBlock =
                "exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML\n" +
                "\tselect * into #DataM\n" +
                "\tfrom openxml (@intDoc, '/root/clue',1)\n" +
                "\twith (LID INT,SalesOrder VARCHAR(255),CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))\n" +
                "\tselect @intLID=LID FROM #DataM";

            String newBlock =
                "    -- 转换的OPENXML处理块\n" +
                "    CREATE TEMP TABLE temp_DataM AS\n" +
                "    SELECT\n" +
                "        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
                "        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
                "        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
                "        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
                "        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n" +
                "\n" +
                "    -- 获取变量值\n" +
                "    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;";

            if (fixedCode.contains(oldBlock)) {
                fixedCode = fixedCode.replace(oldBlock, newBlock);
                result.addWarning("使用字符串替换转换了OPENXML块");
            }
        }

        return fixedCode;
    }

    /**
     * 早期处理完整的OPENXML块
     * 在所有其他转换之前就识别并转换完整的OPENXML模式
     */
    private String fixCompleteOpenXmlBlockEarly(String code, ConversionResult result) {
        String fixedCode = code;

        // 查找您的具体OPENXML模式（使用更宽松的匹配）
        Pattern completePattern = Pattern.compile(
            "exec\\s+sp_xml_preparedocument\\s+@intDoc\\s+OUTPUT,\\s*@chrXML\\s*\\n" +
            "\\s*select\\s+\\*\\s+into\\s+#DataM\\s*\\n" +
            "\\s*from\\s+openxml\\s*\\(\\s*@intDoc,\\s*'/root/clue',\\s*1\\s*\\)\\s*\\n" +
            "\\s*with\\s*\\(LID\\s+INT,SalesOrder\\s+VARCHAR\\(255\\),CustomerInfo\\s+VARCHAR\\(255\\),LostReason\\s+VARCHAR\\(255\\),Suggestion\\s+VARCHAR\\(255\\)\\)\\s*\\n" +
            "\\s*select\\s+@intLID\\s*=\\s*LID\\s+FROM\\s+#DataM",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = completePattern.matcher(fixedCode);
        if (matcher.find()) {
            String replacement =
                "    -- 转换的完整OPENXML处理块\n" +
                "    CREATE TEMP TABLE temp_DataM AS\n" +
                "    SELECT\n" +
                "        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
                "        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
                "        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
                "        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
                "        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n" +
                "\n" +
                "    -- 获取变量值\n" +
                "    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;";

            fixedCode = matcher.replaceFirst(replacement);
            result.addWarning("早期识别并转换了完整的OPENXML处理块");
            return fixedCode;
        }

        // 如果精确匹配失败，尝试更简单的字符串匹配
        String searchPattern =
            "exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML\n" +
            "\tselect * into #DataM\n" +
            "\tfrom openxml (@intDoc, '/root/clue',1)\n" +
            "\twith (LID INT,SalesOrder VARCHAR(255),CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))\n" +
            "\tselect @intLID=LID FROM #DataM";

        if (fixedCode.contains("exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML") &&
            fixedCode.contains("select * into #DataM") &&
            fixedCode.contains("from openxml (@intDoc, '/root/clue',1)") &&
            fixedCode.contains("with (LID INT,SalesOrder VARCHAR(255),CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))") &&
            fixedCode.contains("select @intLID=LID FROM #DataM")) {

            // 使用逐行替换的方式
            String[] lines = fixedCode.split("\\n");
            StringBuilder sb = new StringBuilder();
            boolean inOpenXmlBlock = false;
            boolean blockProcessed = false;

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i];
                String trimmedLine = line.trim();

                // 检测OPENXML块的开始
                if (trimmedLine.contains("exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML")) {
                    inOpenXmlBlock = true;
                    if (!blockProcessed) {
                        // 插入转换后的代码
                        sb.append("    -- 转换的完整OPENXML处理块\n");
                        sb.append("    CREATE TEMP TABLE temp_DataM AS\n");
                        sb.append("    SELECT\n");
                        sb.append("        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n");
                        sb.append("        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n");
                        sb.append("        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n");
                        sb.append("        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n");
                        sb.append("        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n");
                        sb.append("\n");
                        sb.append("    -- 获取变量值\n");
                        sb.append("    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;\n");
                        blockProcessed = true;
                        result.addWarning("使用逐行处理转换了完整的OPENXML块");
                    }
                    continue; // 跳过原始行
                }

                // 检测OPENXML块的结束
                if (inOpenXmlBlock && trimmedLine.contains("select @intLID=LID FROM #DataM")) {
                    inOpenXmlBlock = false;
                    continue; // 跳过原始行
                }

                // 如果在OPENXML块中，跳过所有原始行
                if (inOpenXmlBlock) {
                    continue;
                }

                // 保留其他行
                sb.append(line).append("\n");
            }

            if (blockProcessed) {
                fixedCode = sb.toString();
            }
        }

        return fixedCode;
    }

    /**
     * 最终的OPENXML修复方法
     * 使用最简单直接的字符串操作
     */
    private String ultimateOpenXmlFix(String code, ConversionResult result) {
        String fixedCode = code;

        // 检查是否包含您的原始OPENXML模式的关键部分
        if (fixedCode.contains("exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML") &&
            fixedCode.contains("select * into #DataM") &&
            fixedCode.contains("from openxml (@intDoc, '/root/clue',1)")) {

            System.out.println("检测到原始OPENXML模式，开始修复...");

            // 使用最直接的字符串替换
            String[] searchPatterns = {
                "exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML",
                "\tselect * into #DataM",
                "\tfrom openxml (@intDoc, '/root/clue',1)",
                "\twith (LID INT,SalesOrder VARCHAR(255),CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))",
                "\tselect @intLID=LID FROM #DataM"
            };

            // 查找这些模式在代码中的位置
            boolean allPatternsFound = true;
            for (String pattern : searchPatterns) {
                if (!fixedCode.contains(pattern)) {
                    allPatternsFound = false;
                    System.out.println("未找到模式: " + pattern);
                }
            }

            if (allPatternsFound) {
                System.out.println("所有模式都找到了，开始替换...");

                // 构建完整的原始块
                String originalBlock =
                    "exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML\n" +
                    "\tselect * into #DataM\n" +
                    "\tfrom openxml (@intDoc, '/root/clue',1)\n" +
                    "\twith (LID INT,SalesOrder VARCHAR(255),CustomerInfo VARCHAR(255),LostReason VARCHAR(255),Suggestion VARCHAR(255))\n" +
                    "\tselect @intLID=LID FROM #DataM";

                String replacementBlock =
                    "    -- 转换的完整OPENXML处理块\n" +
                    "    CREATE TEMP TABLE temp_DataM AS\n" +
                    "    SELECT\n" +
                    "        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,\n" +
                    "        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,\n" +
                    "        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,\n" +
                    "        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,\n" +
                    "        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;\n" +
                    "\n" +
                    "    -- 获取变量值\n" +
                    "    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;";

                if (fixedCode.contains(originalBlock)) {
                    fixedCode = fixedCode.replace(originalBlock, replacementBlock);
                    result.addWarning("使用最终修复方法成功转换了OPENXML块");
                    System.out.println("OPENXML块替换成功！");
                } else {
                    System.out.println("完整块匹配失败，尝试逐行替换...");

                    // 如果完整块匹配失败，尝试逐行替换
                    String[] lines = fixedCode.split("\\n");
                    StringBuilder sb = new StringBuilder();
                    boolean inOpenXmlBlock = false;
                    boolean blockReplaced = false;

                    for (int i = 0; i < lines.length; i++) {
                        String line = lines[i];

                        // 检测OPENXML块开始
                        if (line.contains("exec sp_xml_preparedocument @intDoc OUTPUT, @chrXML")) {
                            inOpenXmlBlock = true;
                            if (!blockReplaced) {
                                sb.append(replacementBlock).append("\n");
                                blockReplaced = true;
                                result.addWarning("使用逐行替换成功转换了OPENXML块");
                                System.out.println("使用逐行替换成功！");
                            }
                            continue;
                        }

                        // 如果在OPENXML块中，跳过原始行
                        if (inOpenXmlBlock) {
                            if (line.contains("select @intLID=LID FROM #DataM")) {
                                inOpenXmlBlock = false; // 块结束
                            }
                            continue;
                        }

                        // 保留其他行
                        sb.append(line).append("\n");
                    }

                    if (blockReplaced) {
                        fixedCode = sb.toString();
                    }
                }
            }
        }

        return fixedCode;
    }

}
