package com.converter.service;

import com.converter.model.ConversionRequest;
import com.converter.model.ConversionResult;
import com.converter.converter.DataTypeConverter;
import com.converter.converter.FunctionConverter;
import com.converter.converter.SyntaxConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 存储过程转换服务
 * 负责协调各个转换器完成SQL Server到KingBase的转换
 */
@Service
public class StoredProcedureConverterService {

    private static final Logger logger = LoggerFactory.getLogger(StoredProcedureConverterService.class);

    @Autowired
    private DataTypeConverter dataTypeConverter;

    @Autowired
    private FunctionConverter functionConverter;

    @Autowired
    private SyntaxConverter syntaxConverter;

    /**
     * 转换SQL Server存储过程到KingBase格式
     *
     * @param request 转换请求
     * @return 转换结果
     */
    public ConversionResult convertStoredProcedure(ConversionRequest request) {
        logger.info("开始转换SQL Server存储过程到KingBase格式");
        
        ConversionResult result = new ConversionResult();
        result.setOriginalCode(request.getSqlServerCode());

        try {
            // 预处理：清理和标准化输入代码
            String preprocessedCode = preprocessCode(request.getSqlServerCode());
            
            // 第一步：数据类型转换
            String step1Result = dataTypeConverter.convert(preprocessedCode, result);
            logger.debug("数据类型转换完成");

            // 第二步：函数转换
            String step2Result = functionConverter.convert(step1Result, result);
            logger.debug("函数转换完成");

            // 第三步：语法结构转换
            String step3Result = syntaxConverter.convert(step2Result, result);
            logger.debug("语法结构转换完成");

            // 后处理：格式化和优化
            String finalResult = postprocessCode(step3Result, request.getOptions());

            result.setKingBaseCode(finalResult);
            
            // 设置转换状态
            if (result.getErrors().isEmpty()) {
                if (result.getWarnings().isEmpty()) {
                    result.setStatus(ConversionResult.Status.SUCCESS);
                } else {
                    result.setStatus(ConversionResult.Status.WARNING);
                }
            } else {
                result.setStatus(ConversionResult.Status.ERROR);
            }

            // 更新统计信息
            updateStatistics(result, request.getSqlServerCode(), finalResult);

            logger.info("存储过程转换完成，状态: {}", result.getStatus());

        } catch (Exception e) {
            logger.error("转换过程中发生异常", e);
            result.setStatus(ConversionResult.Status.ERROR);
            result.addError("转换过程中发生异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 预处理代码
     */
    private String preprocessCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("输入的SQL Server代码不能为空");
        }

        // 移除BOM标记
        if (code.startsWith("\uFEFF")) {
            code = code.substring(1);
        }

        // 标准化换行符
        code = code.replaceAll("\\r\\n", "\n").replaceAll("\\r", "\n");

        // 预先移除GO语句（在所有其他处理之前）
        code = code.replaceAll("(?i)\\bGO\\b", "");

        // 移除多余的空白字符和空行
        code = code.replaceAll("\\n\\s*\\n\\s*\\n", "\n\n");
        code = code.trim();

        return code;
    }

    /**
     * 后处理代码
     */
    private String postprocessCode(String code, ConversionRequest.ConversionOptions options) {
        if (code == null) {
            return "";
        }

        String result = code;

        // 格式化输出
        if (options.isFormatOutput()) {
            result = formatCode(result);
        }

        return result;
    }

    /**
     * 格式化代码
     */
    private String formatCode(String code) {
        // 基本的代码格式化
        String[] lines = code.split("\n");
        StringBuilder formatted = new StringBuilder();
        int indentLevel = 0;

        for (String line : lines) {
            String trimmedLine = line.trim();
            
            if (trimmedLine.isEmpty()) {
                formatted.append("\n");
                continue;
            }

            // 减少缩进的关键字
            if (trimmedLine.toUpperCase().startsWith("END") || 
                trimmedLine.toUpperCase().startsWith("ELSE") ||
                trimmedLine.toUpperCase().startsWith("ELSIF")) {
                indentLevel = Math.max(0, indentLevel - 1);
            }

            // 添加缩进
            for (int i = 0; i < indentLevel; i++) {
                formatted.append("    ");
            }
            formatted.append(trimmedLine).append("\n");

            // 增加缩进的关键字
            if (trimmedLine.toUpperCase().startsWith("BEGIN") ||
                trimmedLine.toUpperCase().startsWith("IF") ||
                trimmedLine.toUpperCase().startsWith("WHILE") ||
                trimmedLine.toUpperCase().startsWith("FOR") ||
                trimmedLine.toUpperCase().startsWith("LOOP") ||
                trimmedLine.toUpperCase().startsWith("ELSE")) {
                indentLevel++;
            }
        }

        return formatted.toString();
    }

    /**
     * 更新转换统计信息
     */
    private void updateStatistics(ConversionResult result, String originalCode, String convertedCode) {
        ConversionResult.ConversionStatistics stats = result.getStatistics();
        
        // 计算行数
        stats.setTotalLines(originalCode.split("\n").length);
        stats.setConvertedLines(convertedCode.split("\n").length);
        
        // 这里可以根据实际转换过程中的计数来设置其他统计信息
        // stats.setDataTypeConversions(...);
        // stats.setFunctionConversions(...);
        // stats.setSyntaxConversions(...);
    }
}
