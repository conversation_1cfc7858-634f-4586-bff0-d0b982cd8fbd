package com.converter.controller;

import com.converter.model.ConversionRequest;
import com.converter.model.ConversionResult;
import com.converter.service.StoredProcedureConverterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 转换控制器
 * 处理前端请求，提供转换服务的Web接口
 */
@Controller
public class ConversionController {

    private static final Logger logger = LoggerFactory.getLogger(ConversionController.class);

    @Autowired
    private StoredProcedureConverterService converterService;

    /**
     * 显示主页面
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("conversionRequest", new ConversionRequest());
        return "index";
    }

    /**
     * 处理文本转换请求
     */
    @PostMapping("/convert")
    public String convertText(@Valid @ModelAttribute ConversionRequest request,
                             BindingResult bindingResult,
                             Model model) {
        
        logger.info("收到文本转换请求");

        if (bindingResult.hasErrors()) {
            model.addAttribute("conversionRequest", request);
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "index";
        }

        try {
            ConversionResult result = converterService.convertStoredProcedure(request);
            
            model.addAttribute("conversionRequest", request);
            model.addAttribute("conversionResult", result);
            
            logger.info("文本转换完成，状态: {}", result.getStatus());
            
        } catch (Exception e) {
            logger.error("转换过程中发生异常", e);
            model.addAttribute("conversionRequest", request);
            model.addAttribute("errorMessage", "转换失败: " + e.getMessage());
        }

        return "index";
    }

    /**
     * 处理文件上传转换请求
     */
    @PostMapping("/convert/file")
    public String convertFile(@RequestParam("file") MultipartFile file,
                             @ModelAttribute ConversionRequest.ConversionOptions options,
                             Model model) {
        
        logger.info("收到文件转换请求，文件名: {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            model.addAttribute("conversionRequest", new ConversionRequest());
            model.addAttribute("errorMessage", "请选择要上传的文件");
            return "index";
        }

        try {
            // 读取文件内容
            String fileContent = new String(file.getBytes(), StandardCharsets.UTF_8);
            
            // 创建转换请求
            ConversionRequest request = new ConversionRequest(fileContent);
            request.setOptions(options);

            // 执行转换
            ConversionResult result = converterService.convertStoredProcedure(request);
            
            model.addAttribute("conversionRequest", request);
            model.addAttribute("conversionResult", result);
            model.addAttribute("uploadedFileName", file.getOriginalFilename());
            
            logger.info("文件转换完成，状态: {}", result.getStatus());
            
        } catch (IOException e) {
            logger.error("读取上传文件失败", e);
            model.addAttribute("conversionRequest", new ConversionRequest());
            model.addAttribute("errorMessage", "读取文件失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("转换过程中发生异常", e);
            model.addAttribute("conversionRequest", new ConversionRequest());
            model.addAttribute("errorMessage", "转换失败: " + e.getMessage());
        }

        return "index";
    }

    /**
     * REST API接口 - 转换存储过程
     */
    @PostMapping("/api/convert")
    @ResponseBody
    public ResponseEntity<ConversionResult> convertApi(@Valid @RequestBody ConversionRequest request) {
        
        logger.info("收到API转换请求");

        try {
            ConversionResult result = converterService.convertStoredProcedure(request);
            logger.info("API转换完成，状态: {}", result.getStatus());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("API转换过程中发生异常", e);
            
            ConversionResult errorResult = new ConversionResult();
            errorResult.setStatus(ConversionResult.Status.ERROR);
            errorResult.addError("转换失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/api/health")
    @ResponseBody
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("转换服务运行正常");
    }

    /**
     * 获取支持的转换功能信息
     */
    @GetMapping("/api/features")
    @ResponseBody
    public ResponseEntity<Object> getFeatures() {
        return ResponseEntity.ok(new Object() {
            public final String[] dataTypes = {
                "VARCHAR", "NVARCHAR", "CHAR", "NCHAR", "TEXT", "NTEXT",
                "INT", "BIGINT", "SMALLINT", "TINYINT", "BIT",
                "DECIMAL", "NUMERIC", "FLOAT", "REAL", "MONEY",
                "DATETIME", "DATETIME2", "DATE", "TIME",
                "BINARY", "VARBINARY", "IMAGE", "UNIQUEIDENTIFIER"
            };
            
            public final String[] functions = {
                "LEN", "CHARINDEX", "PATINDEX", "STUFF", "ISNULL",
                "GETDATE", "DATEADD", "DATEDIFF", "DATEPART",
                "RAND", "NEWID", "@@IDENTITY", "@@ROWCOUNT"
            };
            
            public final String[] syntaxFeatures = {
                "CREATE PROCEDURE", "DECLARE", "IF-ELSE", "WHILE",
                "TRY-CATCH", "OUTPUT参数", "SET语句", "PRINT语句"
            };
        });
    }
}
