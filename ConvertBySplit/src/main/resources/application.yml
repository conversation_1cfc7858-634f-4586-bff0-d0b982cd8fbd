server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: sqlserver-kingbase-converter
  
  # Thymeleaf配置
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
      
  # 国际化配置
  messages:
    basename: messages
    encoding: UTF-8
    
  # Web配置
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0

# 日志配置
logging:
  level:
    com.converter: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/converter.log
    max-size: 10MB
    max-history: 30

# 应用自定义配置
converter:
  # 转换器配置
  max-code-length: 100000
  default-timeout: 30000
  
  # 支持的文件类型
  supported-file-types:
    - sql
    - txt
    
  # 转换规则配置
  conversion:
    preserve-comments: true
    format-output: true
    strict-mode: false
    
  # 缓存配置
  cache:
    enabled: false
    max-size: 1000
    expire-after-write: 3600

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true
