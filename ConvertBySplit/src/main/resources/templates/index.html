<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Server到KingBase存储过程转换器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .code-container {
            max-height: 500px;
            overflow-y: auto;
        }
        .result-section {
            margin-top: 30px;
        }
        .statistics-card {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .warning-card {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .error-card {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .success-card {
            background-color: #d1edff;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                    <div class="container">
                        <a class="navbar-brand" href="/">
                            <strong>SQL Server → KingBase 存储过程转换器</strong>
                        </a>
                        <span class="navbar-text">
                            高效、准确的数据库存储过程转换工具
                        </span>
                    </div>
                </nav>
            </div>
        </div>

        <div class="container mt-4">
            <!-- 错误信息显示 -->
            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <strong>错误：</strong> <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>

            <!-- 输入区域 -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-code"></i> SQL Server 存储过程输入
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 文本输入表单 -->
                            <form th:action="@{/convert}" method="post" th:object="${conversionRequest}">
                                <div class="mb-3">
                                    <label for="sqlServerCode" class="form-label">请输入SQL Server存储过程代码：</label>
                                    <textarea class="form-control code-container" 
                                              id="sqlServerCode" 
                                              th:field="*{sqlServerCode}"
                                              rows="15" 
                                              placeholder="CREATE PROCEDURE YourProcedureName
AS
BEGIN
    -- 在这里输入您的SQL Server存储过程代码
    SELECT 'Hello World' AS Message;
END"></textarea>
                                    <div th:if="${#fields.hasErrors('sqlServerCode')}" class="text-danger">
                                        <small th:errors="*{sqlServerCode}"></small>
                                    </div>
                                </div>

                                <!-- 转换选项 -->
                                <div class="mb-3">
                                    <h6>转换选项：</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               th:field="*{options.preserveComments}" id="preserveComments">
                                        <label class="form-check-label" for="preserveComments">
                                            保留注释
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               th:field="*{options.formatOutput}" id="formatOutput">
                                        <label class="form-check-label" for="formatOutput">
                                            格式化输出
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               th:field="*{options.strictMode}" id="strictMode">
                                        <label class="form-check-label" for="strictMode">
                                            严格模式转换
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-exchange-alt"></i> 开始转换
                                </button>
                            </form>

                            <hr>

                            <!-- 文件上传表单 -->
                            <form th:action="@{/convert/file}" method="post" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="file" class="form-label">或者上传SQL文件：</label>
                                    <input class="form-control" type="file" id="file" name="file" accept=".sql,.txt">
                                    <div class="form-text">支持.sql和.txt文件格式</div>
                                </div>
                                
                                <!-- 隐藏的转换选项（与上面保持一致） -->
                                <input type="hidden" name="preserveComments" value="true">
                                <input type="hidden" name="formatOutput" value="true">
                                <input type="hidden" name="strictMode" value="false">

                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-upload"></i> 上传并转换
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 输出区域 -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-database"></i> KingBase 存储过程输出
                            </h5>
                        </div>
                        <div class="card-body">
                            <div th:if="${conversionResult}">
                                <!-- 转换状态 -->
                                <div class="mb-3">
                                    <div th:if="${conversionResult.status.name() == 'SUCCESS'}" 
                                         class="alert alert-success success-card">
                                        <strong>转换成功！</strong> 
                                        <span th:text="${conversionResult.status.description}"></span>
                                    </div>
                                    <div th:if="${conversionResult.status.name() == 'WARNING'}" 
                                         class="alert alert-warning warning-card">
                                        <strong>转换完成但有警告！</strong> 
                                        <span th:text="${conversionResult.status.description}"></span>
                                    </div>
                                    <div th:if="${conversionResult.status.name() == 'ERROR'}" 
                                         class="alert alert-danger error-card">
                                        <strong>转换失败！</strong> 
                                        <span th:text="${conversionResult.status.description}"></span>
                                    </div>
                                </div>

                                <!-- 转换后的代码 -->
                                <div class="mb-3">
                                    <label class="form-label">转换后的KingBase代码：</label>
                                    <div class="position-relative">
                                        <textarea class="form-control code-container" 
                                                  rows="15" 
                                                  readonly 
                                                  th:text="${conversionResult.kingBaseCode}"></textarea>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-secondary position-absolute top-0 end-0 m-2"
                                                onclick="copyToClipboard()">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div th:unless="${conversionResult}">
                                <div class="text-center text-muted">
                                    <i class="fas fa-arrow-left fa-2x mb-3"></i>
                                    <p>请在左侧输入SQL Server存储过程代码，然后点击"开始转换"按钮</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 转换结果详情 -->
            <div th:if="${conversionResult}" class="result-section">
                <div class="row">
                    <!-- 统计信息 -->
                    <div class="col-md-4">
                        <div class="card statistics-card">
                            <div class="card-header">
                                <h6 class="mb-0">转换统计</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="h4 text-primary" th:text="${conversionResult.statistics.totalLines}">0</div>
                                        <small>原始行数</small>
                                    </div>
                                    <div class="col-6">
                                        <div class="h4 text-success" th:text="${conversionResult.statistics.convertedLines}">0</div>
                                        <small>转换行数</small>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="h6 text-info" th:text="${conversionResult.statistics.dataTypeConversions}">0</div>
                                        <small>数据类型</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="h6 text-info" th:text="${conversionResult.statistics.functionConversions}">0</div>
                                        <small>函数转换</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="h6 text-info" th:text="${conversionResult.statistics.syntaxConversions}">0</div>
                                        <small>语法转换</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 警告信息 -->
                    <div class="col-md-4" th:if="${!conversionResult.warnings.empty}">
                        <div class="card warning-card">
                            <div class="card-header">
                                <h6 class="mb-0">警告信息</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li th:each="warning : ${conversionResult.warnings}" class="mb-1">
                                        <small><i class="fas fa-exclamation-triangle text-warning"></i> <span th:text="${warning}"></span></small>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div class="col-md-4" th:if="${!conversionResult.errors.empty}">
                        <div class="card error-card">
                            <div class="card-header">
                                <h6 class="mb-0">错误信息</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li th:each="error : ${conversionResult.errors}" class="mb-1">
                                        <small><i class="fas fa-times-circle text-danger"></i> <span th:text="${error}"></span></small>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">SQL Server到KingBase存储过程转换器 &copy; 2024 - 高效、准确、可靠</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        function copyToClipboard() {
            const textarea = document.querySelector('textarea[readonly]');
            textarea.select();
            document.execCommand('copy');
            
            // 显示复制成功提示
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> 已复制';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }
    </script>
</body>
</html>
