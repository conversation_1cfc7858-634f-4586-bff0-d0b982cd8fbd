package com.converter.service;

import com.converter.model.ConversionRequest;
import com.converter.model.ConversionResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 存储过程转换服务测试类
 */
@SpringBootTest
public class StoredProcedureConverterServiceTest {

    @Autowired
    private StoredProcedureConverterService converterService;

    @Test
    public void testSimpleProcedureConversion() {
        // 准备测试数据
        String sqlServerCode = "CREATE PROCEDURE GetUserInfo\n" +
            "    @UserId INT,\n" +
            "    @UserName NVARCHAR(50) OUTPUT\n" +
            "AS\n" +
            "BEGIN\n" +
            "    DECLARE @Count INT\n" +
            "    SET @Count = 0\n" +
            "    \n" +
            "    SELECT @UserName = Name, @Count = COUNT(*)\n" +
            "    FROM Users \n" +
            "    WHERE Id = @UserId\n" +
            "    \n" +
            "    IF @Count > 0\n" +
            "        PRINT 'User found'\n" +
            "    ELSE\n" +
            "        PRINT 'User not found'\n" +
            "END";

        ConversionRequest request = new ConversionRequest(sqlServerCode);

        // 执行转换
        ConversionResult result = converterService.convertStoredProcedure(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getKingBaseCode());
        assertTrue(result.getStatus() == ConversionResult.Status.SUCCESS || 
                  result.getStatus() == ConversionResult.Status.WARNING);
        
        // 验证转换后的代码包含预期的KingBase语法
        String convertedCode = result.getKingBaseCode();
        assertTrue(convertedCode.contains("CREATE OR REPLACE FUNCTION"));
        assertTrue(convertedCode.contains("$$ LANGUAGE plpgsql"));
    }

    @Test
    public void testDataTypeConversion() {
        String sqlServerCode = "CREATE PROCEDURE TestDataTypes\n" +
            "    @IntParam INT,\n" +
            "    @VarcharParam VARCHAR(100),\n" +
            "    @DatetimeParam DATETIME,\n" +
            "    @BitParam BIT\n" +
            "AS\n" +
            "BEGIN\n" +
            "    DECLARE @LocalVar NVARCHAR(50)\n" +
            "    SET @LocalVar = 'Test'\n" +
            "END";

        ConversionRequest request = new ConversionRequest(sqlServerCode);
        ConversionResult result = converterService.convertStoredProcedure(request);

        assertNotNull(result);
        String convertedCode = result.getKingBaseCode();
        
        // 验证数据类型转换
        assertTrue(convertedCode.contains("INTEGER"));
        assertTrue(convertedCode.contains("VARCHAR"));
        assertTrue(convertedCode.contains("TIMESTAMP"));
        assertTrue(convertedCode.contains("BOOLEAN"));
    }

    @Test
    public void testFunctionConversion() {
        String sqlServerCode = "CREATE PROCEDURE TestFunctions\n" +
            "AS\n" +
            "BEGIN\n" +
            "    DECLARE @Length INT\n" +
            "    DECLARE @CurrentDate DATETIME\n" +
            "    \n" +
            "    SET @Length = LEN('Hello World')\n" +
            "    SET @CurrentDate = GETDATE()\n" +
            "    \n" +
            "    IF ISNULL(@Length, 0) > 0\n" +
            "        PRINT 'Length is positive'\n" +
            "END";

        ConversionRequest request = new ConversionRequest(sqlServerCode);
        ConversionResult result = converterService.convertStoredProcedure(request);

        assertNotNull(result);
        String convertedCode = result.getKingBaseCode();
        
        // 验证函数转换
        assertTrue(convertedCode.contains("LENGTH"));
        assertTrue(convertedCode.contains("NOW"));
        assertTrue(convertedCode.contains("COALESCE"));
    }

    @Test
    public void testEmptyCodeHandling() {
        ConversionRequest request = new ConversionRequest("");
        
        assertThrows(Exception.class, () -> {
            converterService.convertStoredProcedure(request);
        });
    }

    @Test
    public void testNullCodeHandling() {
        ConversionRequest request = new ConversionRequest();
        request.setSqlServerCode(null);
        
        assertThrows(Exception.class, () -> {
            converterService.convertStoredProcedure(request);
        });
    }

    @Test
    public void testConversionOptions() {
        String sqlServerCode = "CREATE PROCEDURE TestOptions\n" +
            "AS\n" +
            "BEGIN\n" +
            "    -- This is a comment\n" +
            "    PRINT 'Hello World'\n" +
            "END";

        ConversionRequest request = new ConversionRequest(sqlServerCode);
        
        // 测试保留注释选项
        request.getOptions().setPreserveComments(true);
        request.getOptions().setFormatOutput(true);
        
        ConversionResult result = converterService.convertStoredProcedure(request);
        
        assertNotNull(result);
        assertNotNull(result.getKingBaseCode());
    }

    @Test
    public void testComplexProcedure() {
        String sqlServerCode = """
            CREATE PROCEDURE ComplexProcedure
                @StartDate DATETIME,
                @EndDate DATETIME,
                @Status NVARCHAR(20),
                @TotalCount INT OUTPUT
            AS
            BEGIN
                DECLARE @TempCount INT
                SET @TempCount = 0
                
                BEGIN TRY
                    WHILE @StartDate <= @EndDate
                    BEGIN
                        SELECT @TempCount = @TempCount + COUNT(*)
                        FROM Orders 
                        WHERE OrderDate = @StartDate 
                          AND Status = @Status
                        
                        SET @StartDate = DATEADD(day, 1, @StartDate)
                    END
                    
                    SET @TotalCount = @TempCount
                    
                END TRY
                BEGIN CATCH
                    SET @TotalCount = -1
                    PRINT 'Error occurred'
                END CATCH
            END
            """;

        ConversionRequest request = new ConversionRequest(sqlServerCode);
        ConversionResult result = converterService.convertStoredProcedure(request);

        assertNotNull(result);
        assertNotNull(result.getKingBaseCode());
        
        // 验证复杂语法转换
        String convertedCode = result.getKingBaseCode();
        assertTrue(convertedCode.contains("CREATE OR REPLACE FUNCTION"));
        assertTrue(convertedCode.contains("WHILE"));
        assertTrue(convertedCode.contains("EXCEPTION"));
    }
}
