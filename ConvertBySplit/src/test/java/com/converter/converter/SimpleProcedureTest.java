package com.converter.converter;

import com.converter.model.ConversionResult;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单的存储过程转换测试
 */
public class SimpleProcedureTest {

    @Test
    public void testSimpleProcedureConversion() {
        SyntaxConverter syntaxConverter = new SyntaxConverter();
        ConversionResult result = new ConversionResult();

        String sqlServerCode = """
            ALTER PROCEDURE dbo.new_CrmClueMSave
             @chrXML      ntext,--xml文档
             @chrReturn   varchar(50) output--返回是否保存成功
            AS
            BEGIN

            delete testt
            insert into testt(test)values(@chrXML)

            declare @chrFlag  char(1)--1新增,2修改
            declare @intDoc   int
            declare @chrError varchar(500)

            END
            """;

        System.out.println("原始代码:");
        System.out.println(sqlServerCode);
        System.out.println("\n开始转换...");

        try {
            String convertedCode = syntaxConverter.convert(sqlServerCode, result);

            System.out.println("\n转换结果:");
            System.out.println(convertedCode);

            System.out.println("\n警告信息:");
            result.getWarnings().forEach(System.out::println);

            System.out.println("\n错误信息:");
            result.getErrors().forEach(System.out::println);

            // 验证转换结果
            assertTrue(convertedCode.contains("CREATE OR REPLACE FUNCTION"));
            assertTrue(convertedCode.contains("chrXML TEXT"));
            assertTrue(convertedCode.contains("chrReturn VARCHAR(50)"));
            assertFalse(convertedCode.contains("output"));
            assertTrue(convertedCode.contains("$BODY$ LANGUAGE plpgsql"));
            assertFalse(convertedCode.contains("@chrXML")); // 变量引用应该移除@符号
            assertTrue(convertedCode.contains("DELETE FROM testt")); // DELETE语句应该添加FROM

        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
            fail("转换过程中发生异常: " + e.getMessage());
        }
    }
}
