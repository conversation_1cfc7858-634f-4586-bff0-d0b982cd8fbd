package com.converter.converter;

import com.converter.model.ConversionResult;

/**
 * 快速测试转换器功能
 */
public class QuickTest {

    public static void main(String[] args) {
        SyntaxConverter syntaxConverter = new SyntaxConverter();
        ConversionResult result = new ConversionResult();
        
        String sqlServerCode = """
            ALTER PROCEDURE dbo.new_CrmClueMSave
             @chrXML      ntext,--xml文档  
             @chrReturn   varchar(50) output--返回是否保存成功  
            AS  
            BEGIN  
            
            delete testt
            insert into testt(test)values(@chrXML)
            
            declare @chrFlag  char(1)--1新增,2修改
            declare @intDoc   int
            declare @chrError varchar(500)
            declare @intLID   int
            declare @intSeqNo int
            declare @chrStaffNo varchar(10)
            
            END
            """;
        
        System.out.println("=== 原始SQL Server代码 ===");
        System.out.println(sqlServerCode);
        System.out.println("\n=== 开始转换 ===");
        
        try {
            String convertedCode = syntaxConverter.convert(sqlServerCode, result);
            
            System.out.println("\n=== 转换后的KingBase代码 ===");
            System.out.println(convertedCode);
            
            System.out.println("\n=== 警告信息 ===");
            if (result.getWarnings().isEmpty()) {
                System.out.println("无警告");
            } else {
                result.getWarnings().forEach(warning -> System.out.println("⚠️ " + warning));
            }
            
            System.out.println("\n=== 错误信息 ===");
            if (result.getErrors().isEmpty()) {
                System.out.println("无错误");
            } else {
                result.getErrors().forEach(error -> System.out.println("❌ " + error));
            }
            
            System.out.println("\n=== 转换完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ 转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
