package com.converter.converter;

import com.converter.model.ConversionResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试OUTPUT参数转换功能
 */
@SpringBootTest
public class OutputParameterTest {

    @Test
    public void testOutputParameterConversion() {
        SyntaxConverter syntaxConverter = new SyntaxConverter();
        ConversionResult result = new ConversionResult();
        
        // 测试包含OUTPUT参数的函数声明
        String sqlServerCode = """
            CREATE FUNCTION new_CrmClueMSave
            chrXML TEXT,-- xml文档
            chrReturn VARCHAR(50) output-- 返回是否保存成功
            AS
            BEGIN
                -- 函数体
            END
            """;
        
        String convertedCode = syntaxConverter.convert(sqlServerCode, result);
        
        // 验证转换结果
        assertNotNull(convertedCode);
        assertTrue(convertedCode.contains("CREATE OR REPLACE FUNCTION"));
        assertTrue(convertedCode.contains("chrXML TEXT"));
        assertTrue(convertedCode.contains("chrReturn VARCHAR(50)"));
        assertFalse(convertedCode.contains("output")); // OUTPUT关键字应该被移除
        assertTrue(convertedCode.contains("$BODY$"));
        
        // 验证警告信息
        assertFalse(result.getWarnings().isEmpty());
        
        System.out.println("转换结果:");
        System.out.println(convertedCode);
        System.out.println("\n警告信息:");
        result.getWarnings().forEach(System.out::println);
    }
    
    @Test
    public void testSimpleOutputParameterRemoval() {
        SyntaxConverter syntaxConverter = new SyntaxConverter();
        ConversionResult result = new ConversionResult();
        
        // 测试简单的OUTPUT参数移除
        String code = "chrReturn VARCHAR(10) output,";
        String processed = syntaxConverter.convertOutputParameters(code, result);
        
        assertFalse(processed.contains("output"));
        assertTrue(processed.contains("chrReturn VARCHAR(10)"));
    }
}
