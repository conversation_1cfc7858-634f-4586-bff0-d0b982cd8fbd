package com.converter.converter;

import com.converter.model.ConversionResult;

/**
 * 测试DECLARE语句转换
 */
public class DeclareTest {

    public static void main(String[] args) {
        SyntaxConverter syntaxConverter = new SyntaxConverter();
        ConversionResult result = new ConversionResult();
        
        String sqlServerCode = """
            ALTER PROCEDURE dbo.new_CrmClueMSave
             @chrXML      ntext,--xml文档  
             @chrReturn   varchar(50) output--返回是否保存成功  
            AS  
            BEGIN  
            
            delete testt
            insert into testt(test)values(@chrXML)
            
            declare @chrFlag  char(1)--1新增,2修改
            declare @intDoc   int  
            declare @chrError varchar(500)
            declare @intLID   int
            declare @intSeqNo int
            declare @chrStaffNo varchar(10)
            
            select @chrFlag = '1'
            
            END
            """;
        
        System.out.println("=== 原始SQL Server代码 ===");
        System.out.println(sqlServerCode);
        System.out.println("\n=== 开始转换 ===");
        
        try {
            String convertedCode = syntaxConverter.convert(sqlServerCode, result);
            
            System.out.println("\n=== 转换后的KingBase代码 ===");
            System.out.println(convertedCode);
            
            System.out.println("\n=== 警告信息 ===");
            if (result.getWarnings().isEmpty()) {
                System.out.println("无警告");
            } else {
                result.getWarnings().forEach(warning -> System.out.println("⚠️ " + warning));
            }
            
            System.out.println("\n=== 错误信息 ===");
            if (result.getErrors().isEmpty()) {
                System.out.println("无错误");
            } else {
                result.getErrors().forEach(error -> System.out.println("❌ " + error));
            }
            
            // 验证转换结果
            System.out.println("\n=== 验证结果 ===");
            if (convertedCode.contains("DECLARE") && convertedCode.indexOf("DECLARE") < convertedCode.indexOf("DELETE")) {
                System.out.println("✅ DECLARE语句正确位置");
            } else {
                System.out.println("❌ DECLARE语句位置错误");
            }
            
            if (!convertedCode.contains("@")) {
                System.out.println("✅ @符号已移除");
            } else {
                System.out.println("❌ 仍有@符号残留");
            }
            
            System.out.println("\n=== 转换完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ 转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
