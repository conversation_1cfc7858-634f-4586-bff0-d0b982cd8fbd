-- 转换后的存储过程示例
-- 原始：SQL Server存储过程 new_CrmClueMSave
-- 目标：KingBase兼容的函数

CREATE OR REPLACE FUNCTION new_CrmClueMSave(
    chrXML TEXT                     -- xml文档
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    chrFlag CHAR(1);               -- 1新增,2修改
    intDoc INTEGER;
    chrError VARCHAR(500);
    intLID INTEGER;
    intSeqNo INTEGER;
    chrStaffNo VARCHAR(10);
    chrTrnDate TIMESTAMP;
    Amount NUMERIC(18,2);
BEGIN
    
    -- 删除测试表数据
    DELETE FROM testt;
    INSERT INTO testt (test) VALUES (chrXML);

    -- 创建临时表来存储XML数据
    CREATE TEMP TABLE temp_DataM AS
    SELECT
        (xpath('/root/DataM/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/DataM/StaffNo/text()', chrXML::xml))[1]::text::VARCHAR AS StaffNo,
        (xpath('/root/DataM/AuStaff/text()', chrXML::xml))[1]::text::VARCHAR AS AuStaff,
        (xpath('/root/DataM/IfActed/text()', chrXML::xml))[1]::text::CHAR AS IfActed,
        (xpath('/root/DataM/LIDName/text()', chrXML::xml))[1]::text::VARCHAR AS LIDName,
        (xpath('/root/DataM/LIDGrade/text()', chrXML::xml))[1]::text::CHAR AS LIDGrade,
        (xpath('/root/DataM/Type/text()', chrXML::xml))[1]::text::CHAR AS Type,
        (xpath('/root/DataM/LindID/text()', chrXML::xml))[1]::text::VARCHAR AS LindID,
        (xpath('/root/DataM/GroupNo/text()', chrXML::xml))[1]::text::VARCHAR AS GroupNo,
        (xpath('/root/DataM/CusNo/text()', chrXML::xml))[1]::text::VARCHAR AS CusNo,
        (xpath('/root/DataM/CusName/text()', chrXML::xml))[1]::text::VARCHAR AS CusName,
        (xpath('/root/DataM/LIDAmt/text()', chrXML::xml))[1]::text::NUMERIC AS LIDAmt,
        (xpath('/root/DataM/Description/text()', chrXML::xml))[1]::text::VARCHAR AS Description,
        (xpath('/root/DataM/FindStaff/text()', chrXML::xml))[1]::text::VARCHAR AS FindStaff,
        (xpath('/root/DataM/ChargeStaff/text()', chrXML::xml))[1]::text::VARCHAR AS ChargeStaff,
        (xpath('/root/DataM/DeptNo/text()', chrXML::xml))[1]::text::VARCHAR AS DeptNo,
        -- 新增字段
        (xpath('/root/DataM/SourceChannel/text()', chrXML::xml))[1]::text::VARCHAR AS SourceChannel,
        (xpath('/root/DataM/DetailedSource/text()', chrXML::xml))[1]::text::VARCHAR AS DetailedSource,
        (xpath('/root/DataM/CustomerType/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerType,
        (xpath('/root/DataM/LocalCity/text()', chrXML::xml))[1]::text::VARCHAR AS LocalCity,
        (xpath('/root/DataM/Contact/text()', chrXML::xml))[1]::text::VARCHAR AS Contact,
        (xpath('/root/DataM/ContactWay/text()', chrXML::xml))[1]::text::VARCHAR AS ContactWay,
        (xpath('/root/DataM/Region/text()', chrXML::xml))[1]::text::VARCHAR AS Region,
        -- 插入之前的字段
        (xpath('/root/DataM/TelePhone/text()', chrXML::xml))[1]::text::VARCHAR AS TelePhone,
        (xpath('/root/DataM/QQ/text()', chrXML::xml))[1]::text::VARCHAR AS QQ,
        (xpath('/root/DataM/Email/text()', chrXML::xml))[1]::text::VARCHAR AS Email,
        (xpath('/root/DataM/Flag/text()', chrXML::xml))[1]::text::INTEGER AS Flag;

    -- 创建附件临时表
    CREATE TEMP TABLE temp_wahorderfiled AS
    SELECT
        (xpath('/root/wahfile/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/wahfile/SeqNo/text()', chrXML::xml))[1]::text::INTEGER AS SeqNo,
        (xpath('/root/wahfile/StaffNo/text()', chrXML::xml))[1]::text::VARCHAR AS StaffNo,
        (xpath('/root/wahfile/FileName/text()', chrXML::xml))[1]::text::VARCHAR AS FileName,
        (xpath('/root/wahfile/FilePath/text()', chrXML::xml))[1]::text::VARCHAR AS FilePath,
        (xpath('/root/wahfile/Memo/text()', chrXML::xml))[1]::text::VARCHAR AS Memo,
        (xpath('/root/wahfile/FileSize/text()', chrXML::xml))[1]::text::VARCHAR AS FileSize,
        (xpath('/root/wahfile/flag/text()', chrXML::xml))[1]::text::INTEGER AS flag;

    -- 获取主要变量
    SELECT LID, Flag INTO intLID, chrFlag FROM temp_DataM LIMIT 1;

    -- 数据保存部分
    BEGIN
        IF chrFlag = '1' THEN
            -- 新增操作
            INSERT INTO crmClueM (
                LIDName, StaffNo, AuStaff, CreateDate, CusName, CusNo, LIDAmt, LIDGrade, IfActed,
                Description, ChargeStaff, FindStaff, Type, LindID, DeptNo, GroupNo,
                -- 新增的字段
                SourceChannel, DetailedSource, CustomerType, City, Contact, ContactWay, Region,
                -- 之前的字段
                Tel, QQWX, EmailQQ, IfBatch
            )
            SELECT 
                LIDName, StaffNo, AuStaff, NOW(), CusName, CusNo, LIDAmt, LIDGrade, IfActed,
                Description, ChargeStaff, FindStaff, Type, LindID, DeptNo, GroupNo,
                -- 新增的字段
                SourceChannel, DetailedSource,
                CASE 
                    WHEN CustomerType = '0' THEN '经销'
                    WHEN CustomerType = '1' THEN '自用'
                    ELSE '未知'
                END AS CustomerType, 
                LocalCity, Contact, ContactWay, Region,
                -- 之前的字段
                TelePhone AS Tel, QQ AS QQWX, Email AS EmailQQ, '0'
            FROM temp_DataM;

            -- 获取新插入的ID
            GET DIAGNOSTICS intLID = ROW_COUNT;
            IF intLID > 0 THEN
                SELECT currval(pg_get_serial_sequence('crmClueM', 'LID')) INTO intLID;
            END IF;

            -- 插入日志
            INSERT INTO CRMAllLog(Type, LindID, StaffNo, LogTime, HostAddress, Access)
            SELECT '3', intLID, StaffNo, NOW(), '', '线索管理：新增销售线索：' || LIDName
            FROM temp_DataM;

            -- 插入员工关联
            INSERT INTO crmClueM_Staff(LID, StaffNo, TrnDate)
            SELECT intLID, StaffNo, NOW()
            FROM temp_DataM;

        ELSIF chrFlag = '2' THEN
            -- 更新操作
            UPDATE crmClueM SET 
                LIDName = b.LIDName,
                CusName = b.CusName,
                CusNo = b.CusNo,
                LIDAmt = b.LIDAmt,
                LIDGrade = b.LIDGrade,
                ChargeStaff = b.ChargeStaff,
                FindStaff = b.FindStaff,
                IfActed = b.IfActed,
                Description = b.Description,
                SourceChannel = b.SourceChannel,
                DetailedSource = b.DetailedSource,
                CustomerType = CASE b.CustomerType
                    WHEN '0' THEN '经销'
                    WHEN '1' THEN '自用'
                    ELSE '未知'
                END,
                City = b.LocalCity,
                Contact = b.Contact,
                ContactWay = b.ContactWay,
                Region = b.Region,
                Tel = b.TelePhone,
                QQWX = b.QQ,
                EmailQQ = b.Email
            FROM temp_DataM b 
            WHERE crmClueM.LID = intLID;

            -- 插入更新日志
            INSERT INTO CRMAllLog(Type, LindID, StaffNo, LogTime, HostAddress, Access)
            SELECT '3', intLID, StaffNo, NOW(), '', '线索管理：修改销售线索：' || LIDName
            FROM temp_DataM;
        END IF;

        -- 处理附件上传
        IF EXISTS(SELECT 1 FROM temp_wahorderfiled) THEN
            INSERT INTO crmD_File (LindID, StaffNo, TrnDate, FileName, FilePath, Memo, FileSize, Type)
            SELECT 
                intLID::VARCHAR(100),
                a.StaffNo,
                NOW(),
                a.FileName,
                a.FilePath,
                a.Memo,
                a.FileSize,
                '3'
            FROM temp_wahorderfiled a;
        END IF;

        -- 清理临时表
        DROP TABLE IF EXISTS temp_DataM;
        DROP TABLE IF EXISTS temp_wahorderfiled;

        -- 返回成功结果
        RETURN '1' || COALESCE(intLID::VARCHAR(100), '0');

    EXCEPTION
        WHEN OTHERS THEN
            -- 清理临时表
            DROP TABLE IF EXISTS temp_DataM;
            DROP TABLE IF EXISTS temp_wahorderfiled;

            -- 错误处理
            RAISE NOTICE '存储过程执行失败: %', SQLERRM;
            RETURN '0';
    END;
END;
$BODY$ LANGUAGE plpgsql;
