2025-06-13 13:51:27 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 33540 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 13:51:27 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 13:51:27 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 13:51:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 13:51:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 13:51:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 13:51:28 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 13:51:28 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 884 ms
2025-06-13 13:51:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 13:51:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 13:51:28 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.75 seconds (JVM running for 2.411)
2025-06-13 13:52:10 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 13:52:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 13:52:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:12:04 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 18180 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:12:04 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:12:04 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:12:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:12:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:12:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:12:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:12:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 737 ms
2025-06-13 14:12:05 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:12:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:12:05 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.476 seconds (JVM running for 1.972)
2025-06-13 14:12:07 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:12:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:12:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:12 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 22700 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:17:12 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:17:12 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:17:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:17:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:17:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:17:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:17:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 720 ms
2025-06-13 14:17:13 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:17:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:17:13 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.455 seconds (JVM running for 1.96)
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:34 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 32200 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:17:34 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:17:34 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:17:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:17:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:17:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:17:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:17:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 714 ms
2025-06-13 14:17:35 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:17:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:17:35 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.524 seconds (JVM running for 2.248)
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:26:33 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 34204 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:26:33 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:26:33 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:26:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:26:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:26:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:26:34 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:26:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 740 ms
2025-06-13 14:26:34 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:26:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:26:35 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.473 seconds (JVM running for 1.944)
2025-06-13 14:26:38 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:26:38 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:26:38 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:28:54 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 26064 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:28:54 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:28:54 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:28:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:28:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:28:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:28:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:28:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 812 ms
2025-06-13 14:28:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:28:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:28:55 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.641 seconds (JVM running for 2.144)
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:32:24 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 29016 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:32:24 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:32:24 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:32:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:32:25 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:32:25 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:32:25 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:32:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 711 ms
2025-06-13 14:32:25 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:32:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:32:25 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.455 seconds (JVM running for 1.945)
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:33:45 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 8044 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:33:45 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:33:45 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:33:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:33:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:33:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:33:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:33:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 726 ms
2025-06-13 14:33:46 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:33:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:33:46 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.468 seconds (JVM running for 1.926)
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:33:50 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:33:50 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:34:01 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:34:01 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:34:01 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:34:01 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:34:01 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:34:02 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:34:02 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:34:02 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:34:02 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:34:02 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:34:09 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:34:09 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:34:09 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:34:09 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:34:09 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:35:54 [http-nio-8080-exec-7] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:35:54 [http-nio-8080-exec-7] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:35:54 [http-nio-8080-exec-7] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:35:54 [http-nio-8080-exec-7] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:35:54 [http-nio-8080-exec-7] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:36:35 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 32804 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:36:35 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:36:35 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:36:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:36:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:36:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:36:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:36:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 828 ms
2025-06-13 14:36:36 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:36:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:36:36 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.638 seconds (JVM running for 2.128)
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:36:37 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:36:37 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:36:44 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 33152 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:36:44 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:36:44 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:36:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:36:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:36:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:36:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:36:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 735 ms
2025-06-13 14:36:45 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:36:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:36:46 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 2.077 seconds (JVM running for 2.916)
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:36:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:36:49 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:40:42 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 29248 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:40:42 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:40:42 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:40:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:40:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:40:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:40:43 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:40:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 712 ms
2025-06-13 14:40:43 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:40:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:40:43 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.418 seconds (JVM running for 1.929)
2025-06-13 14:40:51 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:40:51 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:40:51 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:40:51 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:40:51 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:40:51 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:40:51 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:40:51 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:40:51 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:40:52 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:40:52 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:40:52 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:40:52 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:40:52 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:40:52 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:40:52 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:43:53 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 31956 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:43:53 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:43:53 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:43:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:43:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:43:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:43:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:43:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 731 ms
2025-06-13 14:43:54 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:43:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:43:55 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.446 seconds (JVM running for 1.949)
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:43:58 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:43:58 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:47:13 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 28356 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:47:13 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:47:13 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:47:14 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:47:14 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:47:14 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:47:14 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:47:14 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 719 ms
2025-06-13 14:47:14 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:47:14 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:47:14 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.431 seconds (JVM running for 1.889)
2025-06-13 14:47:19 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:47:19 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:47:19 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:47:19 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:47:19 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:47:19 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:47:19 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:47:19 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:47:19 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:47:20 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:47:20 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:47:20 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:47:20 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:47:20 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:47:20 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:47:20 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:47:31 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 28240 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:47:31 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:47:31 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:47:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:47:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:47:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:47:31 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:47:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 793 ms
2025-06-13 14:47:32 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:47:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:47:32 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 2.095 seconds (JVM running for 2.922)
2025-06-13 14:47:33 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:47:33 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:47:33 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:47:39 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:47:39 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:47:39 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:47:39 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:47:39 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:49:44 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 34856 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:49:44 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:49:44 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:49:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:49:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:49:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:49:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:49:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 720 ms
2025-06-13 14:49:45 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:49:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:49:45 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.427 seconds (JVM running for 1.876)
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:49:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:49:49 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:50:19 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 25428 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:50:19 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:50:19 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:50:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:50:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:50:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:50:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:50:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 805 ms
2025-06-13 14:50:20 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:50:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:50:20 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 2.079 seconds (JVM running for 2.666)
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:50:24 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:50:24 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:52:55 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 20276 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:52:55 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:52:55 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:52:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:52:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:52:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:52:56 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:52:56 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 838 ms
2025-06-13 14:52:56 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:52:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:52:57 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.624 seconds (JVM running for 2.079)
2025-06-13 14:52:57 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 34536 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:52:57 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:52:57 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:52:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:52:58 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:52:58 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:52:59 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:52:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1684 ms
2025-06-13 14:52:59 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:52:59 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-06-13 14:52:59 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-13 14:52:59 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-13 14:52:59 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:53:00 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:53:00 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:53:01 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:53:01 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:53:01 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:53:01 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:53:01 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:53:01 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:53:02 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:53:02 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:53:02 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:53:02 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:53:02 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:53:02 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:53:02 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:53:02 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:53:02 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:53:02 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:53:02 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:53:02 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:53:14 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:53:14 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:53:14 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:53:14 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:53:14 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:53:43 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 31988 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:53:43 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:53:44 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:53:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:53:44 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:53:44 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:53:44 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:53:44 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 722 ms
2025-06-13 14:53:44 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:53:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:53:45 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.414 seconds (JVM running for 1.935)
2025-06-13 14:53:48 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:53:48 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:53:48 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:53:48 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:53:48 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:53:49 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:53:49 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:53:49 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:53:50 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:53:50 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:53:50 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:53:50 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:53:50 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:55:51 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 33224 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:55:51 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:55:51 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:55:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:55:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:55:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:55:51 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:55:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 765 ms
2025-06-13 14:55:52 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:55:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:55:52 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.576 seconds (JVM running for 2.046)
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:56:10 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:56:10 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:57:53 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 26476 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:57:53 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:57:53 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:57:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:57:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:57:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:57:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:57:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 714 ms
2025-06-13 14:57:54 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:57:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:57:54 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.43 seconds (JVM running for 1.903)
2025-06-13 14:57:59 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:57:59 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:57:59 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:58:07 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:58:07 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:58:07 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:58:07 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:58:07 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:58:52 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:58:52 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:58:52 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:58:52 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:58:52 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:35:47 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 34968 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:35:47 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:35:47 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:35:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:35:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:35:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:35:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:35:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 740 ms
2025-06-13 15:35:48 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:35:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:35:48 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.491 seconds (JVM running for 1.984)
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:35:58 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:35:58 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:37:55 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 30996 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:37:55 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:37:55 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:37:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:37:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:37:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:37:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:37:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 718 ms
2025-06-13 15:37:56 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:37:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:37:56 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.426 seconds (JVM running for 1.866)
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:37:57 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:37:57 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:40 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 4472 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:39:40 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:39:40 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:39:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:39:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:39:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:39:41 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:39:41 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 777 ms
2025-06-13 15:39:41 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:39:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:39:41 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.499 seconds (JVM running for 2.066)
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:43 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:43 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:54 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:54 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:54 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:54 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:54 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:55 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:55 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:55 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:55 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:55 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:57 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:57 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:57 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:57 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:57 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-5] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:58 [http-nio-8080-exec-5] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:58 [http-nio-8080-exec-5] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-5] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-5] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-6] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:58 [http-nio-8080-exec-6] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:58 [http-nio-8080-exec-6] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-6] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-6] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-7] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:58 [http-nio-8080-exec-7] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:58 [http-nio-8080-exec-7] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-7] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-7] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-8] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:58 [http-nio-8080-exec-8] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:58 [http-nio-8080-exec-8] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-8] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-8] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-9] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:58 [http-nio-8080-exec-9] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:58 [http-nio-8080-exec-9] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:58 [http-nio-8080-exec-9] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:58 [http-nio-8080-exec-9] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:59 [http-nio-8080-exec-10] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:59 [http-nio-8080-exec-10] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:59 [http-nio-8080-exec-10] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-10] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:59 [http-nio-8080-exec-10] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:59 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:59 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:59 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:59 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:39:59 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:39:59 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:39:59 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:39:59 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:39:59 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:41:22 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 30968 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:41:22 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:41:22 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:41:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:41:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:41:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:41:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:41:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 727 ms
2025-06-13 15:41:23 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:41:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:41:23 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.433 seconds (JVM running for 1.903)
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:41:28 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:41:28 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:41:43 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 29668 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:41:43 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:41:43 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:41:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:41:44 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:41:44 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:41:44 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:41:44 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1007 ms
2025-06-13 15:41:44 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:41:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:41:44 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 2.208 seconds (JVM running for 3.19)
2025-06-13 15:41:46 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:41:46 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:41:46 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 15:41:46 [http-nio-8080-exec-1] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-06-13 15:41:47 [http-nio-8080-exec-2] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-06-13 15:41:49 [http-nio-8080-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-06-13 15:41:52 [http-nio-8080-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported]
2025-06-13 15:42:05 [http-nio-8080-exec-6] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:42:05 [http-nio-8080-exec-6] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:42:05 [http-nio-8080-exec-6] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:42:05 [http-nio-8080-exec-6] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:42:05 [http-nio-8080-exec-6] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:45:38 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 23736 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:45:38 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:45:38 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:45:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:45:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:45:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:45:39 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:45:39 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 735 ms
2025-06-13 15:45:39 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:45:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:45:39 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.53 seconds (JVM running for 2.147)
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:45:44 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:45:44 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:46:00 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:46:00 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:46:00 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:46:00 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:46:00 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:46:15 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:46:15 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:46:15 [http-nio-8080-exec-4] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:46:15 [http-nio-8080-exec-4] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:46:15 [http-nio-8080-exec-4] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 15:48:57 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 24408 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 15:48:57 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 15:48:57 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 15:48:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 15:48:58 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 15:48:58 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 15:48:58 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 15:48:58 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 738 ms
2025-06-13 15:48:58 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 15:48:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 15:48:58 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.551 seconds (JVM running for 2.03)
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 15:49:10 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 15:49:10 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 16:02:46 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 34140 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 16:02:46 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 16:02:46 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 16:02:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 16:02:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 16:02:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 16:02:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 16:02:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 887 ms
2025-06-13 16:02:48 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 16:02:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 16:02:48 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.733 seconds (JVM running for 2.322)
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 16:02:50 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 16:02:50 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 16:04:29 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 14688 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 16:04:29 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 16:04:29 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 16:04:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 16:04:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 16:04:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 16:04:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 16:04:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1512 ms
2025-06-13 16:04:30 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 16:04:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 16:04:31 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 2.608 seconds (JVM running for 3.135)
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 16:04:31 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 16:04:31 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
