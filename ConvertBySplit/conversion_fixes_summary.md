# SQL Server到KingBase存储过程转换修复总结

## 主要问题和修复方案

### 1. 函数参数和OUTPUT参数问题
**问题**: 转换后的函数缺少参数定义，OUTPUT参数处理不正确
```sql
-- 原始SQL Server代码
ALTER PROCEDURE dbo.new_CrmClueMSave
    @chrXML NTEXT,
    @chrReturn VARCHAR(50) OUTPUT

-- 错误的转换结果
CREATE OR REPLACE FUNCTION new_CrmClueMSave() RETURNS VOID AS $BODY$

-- 正确的转换结果
CREATE OR REPLACE FUNCTION new_CrmClueMSave(
    chrXML TEXT                     -- xml文档
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    -- 其他变量声明，不声明返回值变量
    chrFlag CHAR(1);
BEGIN
    -- 业务逻辑
    IF chrFlag = '1' THEN
        RETURN '1Success';
    ELSE
        RETURN '0Failed';
    END IF;
END;
```

**关键点**:
- OUTPUT参数转换为函数返回值
- **不要在DECLARE中声明返回值变量**
- 直接使用RETURN语句返回结果
- 避免变量名与返回类型冲突

### 2. OPENXML转换问题
**问题**: `SELECT * INTO #table FROM openxml(...)` 转换不完整
```sql
-- 原始SQL Server代码
select * into #DataM   
from openxml (@intDoc, '/root/DataM',1)  
with (LID INT, StaffNo varchar(5), ...)

-- 正确的KingBase转换
CREATE TEMP TABLE temp_DataM AS
SELECT
    (xpath('/root/DataM/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
    (xpath('/root/DataM/StaffNo/text()', chrXML::xml))[1]::text::VARCHAR AS StaffNo,
    ...
```

### 3. 变量赋值语法问题
**问题**: SQL Server的变量赋值语法转换错误
```sql
-- 错误的转换
select intLID=LID,chrFlag=Flag FROM temp_DataM;

-- 正确的转换
SELECT LID, Flag INTO intLID, chrFlag FROM temp_DataM LIMIT 1;
```

### 4. IF语句语法问题
**问题**: IF语句语法转换错误
```sql
-- 错误的转换
BEGIN;chrFlag='1' THEN

-- 正确的转换
IF chrFlag = '1' THEN
```

### 5. 事务语法问题
**问题**: 事务和错误处理语法转换错误
```sql
-- 错误的转换
ROLLBACK;chrError='0新增失败';

-- 正确的转换
ROLLBACK;
chrError := '0新增失败';
-- 或者使用异常处理
EXCEPTION
    WHEN OTHERS THEN
        chrReturn := '0';
        RAISE NOTICE '存储过程执行失败: %', SQLERRM;
```

### 6. 孤立的SELECT表达式问题
**问题**: 转换后出现孤立的SELECT表达式
```sql
-- 错误的转换结果
(xpath('/root/DataM/LID/text()', intDoc::xml))[1]::text::INTEGER AS LID,

-- 应该注释掉或整合到完整的SELECT语句中
-- 注释掉的孤立SELECT表达式：
-- (xpath('/root/DataM/LID/text()', intDoc::xml))[1]::text::INTEGER AS LID,
```

### 7. 字段定义位置问题
**问题**: 字段定义出现在错误的位置
```sql
-- 错误的位置
FROM (SELECT 1) AS dummy_table,AuStaff varchar(5), IfActed CHAR(1)...

-- 正确的处理
FROM (SELECT 1) AS dummy_table;
-- 注意：以下字段定义应该在CREATE TABLE语句中定义
-- AuStaff varchar(5), IfActed CHAR(1)...
```

## 转换器改进建议

### 1. 改进OPENXML处理
- 正确识别 `SELECT * INTO #table FROM openxml(...)` 模式
- 将WITH子句中的字段定义转换为xpath表达式
- 生成完整的CREATE TEMP TABLE AS SELECT语句

### 2. 改进参数处理
- 保留原始存储过程的参数定义
- 正确转换OUTPUT参数为OUT参数
- 处理参数的数据类型映射

### 3. 改进语法修复
- 修复变量赋值语法
- 修复IF语句语法
- 修复事务处理语法
- 清理孤立的SQL片段

### 4. 改进错误处理
- 将SQL Server的错误处理转换为PostgreSQL的异常处理
- 正确处理@@ERROR和@@IDENTITY等系统变量

### 5. 改进注释处理
- 正确识别注释块的开始和结束
- 区分真正的注释和被注释的代码
- 避免将完整的存储过程定义当作注释处理

## 测试建议

1. **语法验证**: 转换后的代码应该能够在KingBase中成功编译
2. **功能测试**: 验证转换后的函数功能与原始存储过程一致
3. **性能测试**: 确保转换后的性能可接受
4. **边界测试**: 测试各种边界情况和异常情况

## 使用建议

1. **分步转换**: 对于复杂的存储过程，建议分步骤进行转换和测试
2. **手动检查**: 转换后需要手动检查和调整
3. **测试验证**: 在生产环境使用前进行充分测试
4. **文档记录**: 记录转换过程中的修改和注意事项
