# 表提示和数据类型修复总结

## 问题描述

在SQL Server到KingBase的转换中，遇到了两个主要问题：

### 1. 数据类型问题
```sql
-- 错误：KingBase不支持VARCHAR(max)
chrSql VARCHAR(max);

-- 错误信息：
ERROR: 语法错误 在 "max" 或附近的
CONTEXT: 无效的类型名字 "VARCHAR(max)"
```

### 2. 表提示问题
```sql
-- 错误：KingBase不支持WITH(NOLOCK)
IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WITH(NOLOCK) WHERE DocNo=chrNo AND AuCode='014')

-- 错误信息：
ERROR: 语法错误 在 "WITH" 或附近的
```

## 修复方案

### 1. 数据类型修复

#### 在DataTypeConverter中添加了特殊数据类型处理：

**`handleSpecialDataTypes`方法**：
- `VARCHAR(max)` → `TEXT`
- `NVARCHAR(max)` → `TEXT`
- `VARBINARY(max)` → `BYTEA`
- `CHAR(max)` → `TEXT`
- `NCHAR(max)` → `TEXT`
- `BINARY(max)` → `BYTEA`

#### 在SyntaxConverter中添加了数据类型问题修复：

**`fixDataTypeIssues`方法**：
- 作为后备修复机制
- 处理可能遗漏的数据类型问题

### 2. 表提示修复

#### 在SyntaxConverter中添加了表提示修复：

**`fixTableHints`方法**处理以下SQL Server表提示：

- `WITH(NOLOCK)` - 移除（最常见）
- `WITH(READUNCOMMITTED)` - 移除
- `WITH(READCOMMITTED)` - 移除
- `WITH(REPEATABLEREAD)` - 移除
- `WITH(SERIALIZABLE)` - 移除
- `WITH(READPAST)` - 移除
- `WITH(UPDLOCK)` - 移除
- `WITH(XLOCK)` - 移除
- `WITH(TABLOCK)` - 移除
- `WITH(TABLOCKX)` - 移除
- `WITH(PAGLOCK)` - 移除
- `WITH(ROWLOCK)` - 移除
- `WITH(NOWAIT)` - 移除
- `WITH(INDEX(...))` - 移除索引提示

## 转换对比

### 数据类型转换
```sql
-- SQL Server原始代码
DECLARE @chrSql VARCHAR(max);
DECLARE @chrData NVARCHAR(max);
DECLARE @binData VARBINARY(max);

-- KingBase转换后代码
DECLARE
    chrSql TEXT;
    chrData TEXT;
    binData BYTEA;
```

### 表提示转换
```sql
-- SQL Server原始代码
SELECT * FROM table1 WITH(NOLOCK)
SELECT * FROM table2 WITH(READUNCOMMITTED)
SELECT * FROM table3 WITH(INDEX(idx_name))

-- KingBase转换后代码
SELECT * FROM table1
SELECT * FROM table2
SELECT * FROM table3
```

### 完整示例转换
```sql
-- SQL Server原始代码
CREATE PROCEDURE test_proc
    @chrSql VARCHAR(max)
AS
BEGIN
    IF EXISTS(SELECT 1 FROM table1 WITH(NOLOCK) WHERE id=1)
        PRINT 'Found'
END

-- KingBase转换后代码
CREATE OR REPLACE FUNCTION test_proc(
    chrSql TEXT
) RETURNS VOID AS $BODY$
BEGIN
    IF EXISTS(SELECT 1 FROM table1 WHERE id=1) THEN
        RAISE NOTICE 'Found';
    END IF;
END;
$BODY$ LANGUAGE plpgsql;
```

## 实现细节

### 1. 数据类型处理流程
1. **DataTypeConverter.handleSpecialDataTypes()** - 处理特殊数据类型
2. **DataTypeConverter.convertParameterDataTypes()** - 转换参数数据类型
3. **DataTypeConverter.convertVariableDataTypes()** - 转换变量数据类型
4. **SyntaxConverter.fixDataTypeIssues()** - 后备修复

### 2. 表提示处理流程
1. **SyntaxConverter.fixTableHints()** - 识别并移除表提示
2. **正则表达式匹配** - 精确匹配各种表提示语法
3. **警告记录** - 记录所有移除的表提示

### 3. 正则表达式模式
```java
// VARCHAR(max)模式
Pattern varcharMaxPattern = Pattern.compile(
    "\\b(N?VARCHAR)\\s*\\(\\s*max\\s*\\)",
    Pattern.CASE_INSENSITIVE
);

// WITH(NOLOCK)模式
Pattern nolockPattern = Pattern.compile(
    "\\s+WITH\\s*\\(\\s*NOLOCK\\s*\\)",
    Pattern.CASE_INSENSITIVE
);
```

## 注意事项

### 1. 性能影响
- **NOLOCK移除**：在SQL Server中NOLOCK允许脏读以提高性能，移除后可能影响并发性能
- **建议**：在KingBase中使用适当的事务隔离级别

### 2. 数据类型选择
- **TEXT vs VARCHAR(n)**：TEXT类型没有长度限制，但某些情况下可能需要指定具体长度
- **建议**：根据实际数据长度需求选择合适的类型

### 3. 索引提示
- **INDEX提示移除**：可能影响查询性能
- **建议**：在KingBase中创建适当的索引来优化查询

## 测试验证

创建了测试文件 `test_table_hints_fix.sql` 来验证修复效果：
- 展示原始错误代码
- 展示修复后的正确代码
- 提供完整的函数示例

## 使用建议

1. **重新转换**：使用更新后的转换器重新转换存储过程
2. **性能测试**：测试移除表提示后的性能影响
3. **索引优化**：根据需要在KingBase中创建索引
4. **事务管理**：合理设置事务隔离级别
