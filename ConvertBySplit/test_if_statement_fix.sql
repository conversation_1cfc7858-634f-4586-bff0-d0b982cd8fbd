-- 测试IF语句语法修复的示例

-- 原始错误的转换结果
/*
IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
    BEGIN
        SET chrIfAuCode='1'
    END;
*/

-- 修复后的正确语法
IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
    chrIfAuCode := '1';
END IF;

-- 完整的函数示例
CREATE OR REPLACE FUNCTION test_if_statement_fix(
    chrNo VARCHAR(30)
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    chrIfAuCode CHAR(1);
    chrDocCode VARCHAR(10);
BEGIN
    -- 初始化变量
    chrIfAuCode := '0';
    chrDocCode := '08';

    -- 修复后的IF语句语法
    IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
        chrIfAuCode := '1';
    END IF;

    -- 嵌套IF语句示例
    IF chrDocCode = '08' THEN
        IF chrIfAuCode = '1' THEN
            RETURN 'Success';
        ELSE
            RETURN 'Not Found';
        END IF;
    ELSE
        RETURN 'Invalid DocCode';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Error';
END;
$BODY$ LANGUAGE plpgsql;

-- IF语句语法对比
/*
SQL Server语法：
IF condition
BEGIN
    statements
END

KingBase/PostgreSQL语法：
IF condition THEN
    statements;
END IF;
*/

-- 复杂IF语句示例
CREATE OR REPLACE FUNCTION complex_if_example(
    input_value INTEGER
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    result_text VARCHAR(50);
BEGIN
    -- 多条件IF语句
    IF input_value > 100 THEN
        result_text := 'High';
    ELSIF input_value > 50 THEN
        result_text := 'Medium';
    ELSIF input_value > 0 THEN
        result_text := 'Low';
    ELSE
        result_text := 'Invalid';
    END IF;

    -- 嵌套IF语句
    IF result_text = 'High' THEN
        IF input_value > 1000 THEN
            result_text := 'Very High';
        END IF;
    END IF;

    RETURN result_text;
END;
$BODY$ LANGUAGE plpgsql;

-- 测试调用
-- SELECT test_if_statement_fix('TEST001');
-- SELECT complex_if_example(150);
