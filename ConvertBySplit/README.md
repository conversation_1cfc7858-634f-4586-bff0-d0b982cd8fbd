# SQL Server到KingBase存储过程转换器

一个高效、准确的SQL Server存储过程到KingBase数据库存储过程的自动转换工具。

## 项目概述

本项目提供了一个完整的Web应用程序，能够将SQL Server存储过程自动转换为KingBase数据库兼容的存储过程格式。系统采用Java + Spring Boot技术栈开发，提供友好的Web界面和REST API接口。

## 主要功能

### 🔄 核心转换功能
- **数据类型转换**：自动映射SQL Server数据类型到KingBase兼容类型
- **函数转换**：转换SQL Server特有函数为KingBase等效函数
- **语法结构转换**：处理存储过程声明、变量声明、控制流等语法差异

### 🌐 用户界面
- **Web界面**：直观的在线转换界面
- **文本输入**：支持直接粘贴SQL Server存储过程代码
- **文件上传**：支持上传.sql和.txt文件进行批量转换
- **实时预览**：即时显示转换结果

### 📊 转换报告
- **转换状态**：成功/警告/错误状态显示
- **详细统计**：转换行数、类型转换数量等统计信息
- **警告信息**：需要手动检查的转换项目
- **错误报告**：转换失败的详细原因

### 🔧 转换选项
- **保留注释**：可选择是否保留原始注释
- **格式化输出**：自动格式化转换后的代码
- **严格模式**：更严格的转换规则

## 支持的转换项目

### 数据类型映射
| SQL Server | KingBase |
|------------|----------|
| VARCHAR/NVARCHAR | VARCHAR |
| INT | INTEGER |
| DATETIME | TIMESTAMP |
| BIT | BOOLEAN |
| MONEY | DECIMAL(19,4) |
| UNIQUEIDENTIFIER | UUID |

### 函数转换
| SQL Server | KingBase |
|------------|----------|
| LEN() | LENGTH() |
| GETDATE() | NOW() |
| ISNULL() | COALESCE() |
| CHARINDEX() | POSITION() |
| DATEPART() | EXTRACT() |

### 语法转换
- CREATE PROCEDURE → CREATE OR REPLACE FUNCTION
- DECLARE @variable → DECLARE variable
- SET @variable = value → variable := value
- PRINT → RAISE NOTICE
- TRY-CATCH → EXCEPTION WHEN OTHERS

## 技术架构

### 后端技术栈
- **Java 11**：核心开发语言
- **Spring Boot 2.7.14**：应用框架
- **Maven**：项目管理和构建工具
- **Thymeleaf**：模板引擎
- **JSQLParser**：SQL解析库

### 前端技术
- **Bootstrap 5**：UI框架
- **Thymeleaf**：服务端模板渲染
- **JavaScript**：交互功能
- **Font Awesome**：图标库

## 快速开始

### 环境要求
- Java 11 或更高版本
- Maven 3.6 或更高版本

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd ConvertBySplit
```

2. **编译项目**
```bash
mvn clean compile
```

3. **运行测试**
```bash
mvn test
```

4. **启动应用**
```bash
mvn spring-boot:run
```

5. **访问应用**
打开浏览器访问：http://localhost:8080

### Docker部署（可选）
```bash
# 构建镜像
docker build -t sqlserver-kingbase-converter .

# 运行容器
docker run -p 8080:8080 sqlserver-kingbase-converter
```

## 使用指南

### Web界面使用
1. 访问主页面
2. 在左侧文本框中输入SQL Server存储过程代码
3. 选择转换选项
4. 点击"开始转换"按钮
5. 在右侧查看转换结果

### 文件上传
1. 点击"选择文件"按钮
2. 选择.sql或.txt格式的文件
3. 点击"上传并转换"
4. 查看转换结果

### REST API使用
```bash
# 转换存储过程
curl -X POST http://localhost:8080/api/convert \
  -H "Content-Type: application/json" \
  -d '{
    "sqlServerCode": "CREATE PROCEDURE test AS BEGIN SELECT 1 END",
    "options": {
      "preserveComments": true,
      "formatOutput": true,
      "strictMode": false
    }
  }'

# 健康检查
curl http://localhost:8080/api/health

# 获取支持的功能
curl http://localhost:8080/api/features
```

## 项目结构

```
src/
├── main/
│   ├── java/com/converter/
│   │   ├── ConverterApplication.java          # 主启动类
│   │   ├── controller/
│   │   │   └── ConversionController.java      # Web控制器
│   │   ├── service/
│   │   │   └── StoredProcedureConverterService.java  # 转换服务
│   │   ├── converter/
│   │   │   ├── DataTypeConverter.java         # 数据类型转换器
│   │   │   ├── FunctionConverter.java         # 函数转换器
│   │   │   └── SyntaxConverter.java           # 语法转换器
│   │   └── model/
│   │       ├── ConversionRequest.java         # 转换请求模型
│   │       └── ConversionResult.java          # 转换结果模型
│   └── resources/
│       ├── templates/
│       │   └── index.html                     # 主页面模板
│       └── application.yml                    # 应用配置
└── test/
    └── java/com/converter/
        └── service/
            └── StoredProcedureConverterServiceTest.java  # 服务测试
```

## 配置说明

### 应用配置（application.yml）
- 服务器端口：8080
- 文件上传限制：10MB
- 日志级别：DEBUG（开发环境）
- 转换超时：30秒

### 自定义配置
```yaml
converter:
  max-code-length: 100000      # 最大代码长度
  default-timeout: 30000       # 默认超时时间
  supported-file-types:        # 支持的文件类型
    - sql
    - txt
```

## 开发指南

### 添加新的数据类型转换
1. 在`DataTypeConverter.java`中的`DATA_TYPE_MAPPING`添加映射
2. 更新相关的转换逻辑
3. 添加单元测试

### 添加新的函数转换
1. 在`FunctionConverter.java`中的`FUNCTION_MAPPING`添加映射
2. 对于复杂函数，实现专门的转换方法
3. 添加测试用例

### 扩展语法转换
1. 在`SyntaxConverter.java`中添加新的转换方法
2. 使用正则表达式匹配和替换
3. 确保添加适当的警告信息

## 测试

### 运行所有测试
```bash
mvn test
```

### 运行特定测试类
```bash
mvn test -Dtest=StoredProcedureConverterServiceTest
```

### 测试覆盖率
```bash
mvn jacoco:report
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 项目讨论区

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础的存储过程转换功能
- Web界面和REST API
- 数据类型、函数、语法转换支持
