-- 测试IF EXISTS语句修复的示例

-- 原始错误的转换结果
/*
IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
    BEGIN
        SET chrIfAuCode='1'
    END;
*/

-- 修复后的正确语法
IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
    chrIfAuCode := '1';
END IF;

-- 完整的函数示例
CREATE OR REPLACE FUNCTION test_if_exists_fix(
    chrNo VARCHAR(30)
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    chrIfAuCode CHAR(1);
    chrDocCode VARCHAR(10);
    chrStatus VARCHAR(20);
BEGIN
    -- 初始化变量
    chrIfAuCode := '0';
    chrDocCode := '08';
    chrStatus := 'PENDING';

    -- 修复后的IF EXISTS语句
    IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='014') THEN
        chrIfAuCode := '1';
    END IF;

    -- 多个IF EXISTS语句
    IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='015') THEN
        chrStatus := 'APPROVED';
    END IF;

    IF EXISTS(SELECT 1 FROM flwFlowRecord_08 WHERE DocNo=chrNo AND AuCode='016') THEN
        chrStatus := 'REJECTED';
    END IF;

    -- 嵌套IF语句
    IF chrIfAuCode = '1' THEN
        IF chrStatus = 'APPROVED' THEN
            RETURN 'SUCCESS';
        ELSIF chrStatus = 'REJECTED' THEN
            RETURN 'FAILED';
        ELSE
            RETURN 'PENDING';
        END IF;
    ELSE
        RETURN 'NOT_FOUND';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR';
END;
$BODY$ LANGUAGE plpgsql;

-- IF语句语法转换对比
/*
SQL Server语法：
IF EXISTS(SELECT 1 FROM table WHERE condition)
BEGIN
    SET @variable = 'value'
END

KingBase/PostgreSQL语法：
IF EXISTS(SELECT 1 FROM table WHERE condition) THEN
    variable := 'value';
END IF;
*/

-- 复杂的IF EXISTS示例
CREATE OR REPLACE FUNCTION complex_if_exists_example(
    doc_no VARCHAR(30),
    user_id INTEGER
) RETURNS VARCHAR(100) AS $BODY$
DECLARE
    access_level INTEGER;
    department_code VARCHAR(10);
    result_message VARCHAR(100);
BEGIN
    -- 检查用户权限
    IF EXISTS(SELECT 1 FROM user_permissions WHERE user_id = user_id AND permission = 'READ') THEN
        access_level := 1;
    END IF;

    IF EXISTS(SELECT 1 FROM user_permissions WHERE user_id = user_id AND permission = 'WRITE') THEN
        access_level := 2;
    END IF;

    IF EXISTS(SELECT 1 FROM user_permissions WHERE user_id = user_id AND permission = 'ADMIN') THEN
        access_level := 3;
    END IF;

    -- 检查部门代码
    IF EXISTS(SELECT 1 FROM departments d JOIN users u ON d.id = u.department_id WHERE u.id = user_id) THEN
        SELECT d.code INTO department_code 
        FROM departments d 
        JOIN users u ON d.id = u.department_id 
        WHERE u.id = user_id;
    END IF;

    -- 根据权限级别返回结果
    IF access_level >= 2 THEN
        result_message := 'Access granted for document: ' || doc_no || ' in department: ' || department_code;
    ELSIF access_level = 1 THEN
        result_message := 'Read-only access for document: ' || doc_no;
    ELSE
        result_message := 'Access denied';
    END IF;

    RETURN result_message;

EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Error checking permissions: ' || SQLERRM;
END;
$BODY$ LANGUAGE plpgsql;

-- 测试调用
-- SELECT test_if_exists_fix('DOC001');
-- SELECT complex_if_exists_example('DOC001', 123);
