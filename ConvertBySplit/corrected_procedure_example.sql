-- 正确转换后的存储过程示例
-- 原始：SQL Server存储过程 CrmClueMReadSave1
-- 目标：KingBase兼容的函数

CREATE OR REPLACE FUNCTION CrmClueMReadSave1(
    chrXML TEXT
) RETURNS VARCHAR(50) AS $BODY$
DECLARE
    chrFlag CHAR(1);               -- 1新增,2修改
    intLID INTEGER;
    chrError VARCHAR(500);
    intDoc INTEGER;
BEGIN
    -- 删除测试表数据
    DELETE FROM testt;
    INSERT INTO testt (test) VALUES (chrXML);

    -- 传入参数解析部分
    -- 创建临时表来存储XML数据
    CREATE TEMP TABLE temp_DataM AS
    SELECT
        (xpath('/root/clue/LID/text()', chrXML::xml))[1]::text::INTEGER AS LID,
        (xpath('/root/clue/SalesOrder/text()', chrXML::xml))[1]::text::VARCHAR AS SalesOrder,
        (xpath('/root/clue/CustomerInfo/text()', chrXML::xml))[1]::text::VARCHAR AS CustomerInfo,
        (xpath('/root/clue/LostReason/text()', chrXML::xml))[1]::text::VARCHAR AS LostReason,
        (xpath('/root/clue/Suggestion/text()', chrXML::xml))[1]::text::VARCHAR AS Suggestion;

    -- 获取LID值
    SELECT LID INTO intLID FROM temp_DataM LIMIT 1;

    -- 更新主表数据
    UPDATE crmClueM 
    SET 
        SalesOrder = b.SalesOrder,
        CustomerInfo = b.CustomerInfo,
        LostReason = b.LostReason,
        Suggestion = b.Suggestion
    FROM temp_DataM b 
    WHERE crmClueM.LID = b.LID AND crmClueM.LID = intLID;

    -- 清理临时表
    DROP TABLE IF EXISTS temp_DataM;

    -- 返回成功结果
    RETURN '1';

EXCEPTION
    WHEN OTHERS THEN
        -- 清理临时表
        DROP TABLE IF EXISTS temp_DataM;
        
        -- 错误处理
        RAISE NOTICE '存储过程执行失败: %', SQLERRM;
        RETURN '0';
END;
$BODY$ LANGUAGE plpgsql;
